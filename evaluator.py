import pandas as pd
from dotenv import load_dotenv
from llama_index.core.evaluation import (
	FaithfulnessEvaluator,
	AnswerRelevancyEvaluator,
	CorrectnessEvaluator,
	SemanticSimilarityEvaluator
)
from core.ragPipeline import AdvancedRAGPipeline
import time

load_dotenv()

# === Load CSV ===
qa_file = "qa_pairs_all.csv"
df = pd.read_csv(qa_file)

# === Init pipeline ===
rag = AdvancedRAGPipeline()
rag.load_existing_index()
rag.setup_chat_engine()

# === Init evaluators ===
faithfulness_evaluator = FaithfulnessEvaluator()
relevancy_evaluator = AnswerRelevancyEvaluator()
correctness_evaluator = CorrectnessEvaluator()
similarity_evaluator = SemanticSimilarityEvaluator()

print("🔧 Initialized evaluators: Faithfulness, Relevancy, Correctness, Similarity")

# === Run evaluation ===
results = []
total_questions = len(df)

for i, row in df.iterrows():
	query = row["query"]
	expected = row["reference"]
	
	print(f"\n📝 Processing Q{i+1}/{total_questions}: {query[:60]}...")
	
	try:
		# Reset chat history to ensure independence between questions
		rag.reset_chat()
		
		# Get RAG response
		start_time = time.time()
		rag_output = rag.chat(query)
		response_time = time.time() - start_time
		response_obj = rag_output["response_obj"]
		response_text = rag_output["response"]
		sources = rag_output.get("sources", [])

		# Initialize scores
		scores = {
			"faithfulness_score": 0.0,
			"faithfulness_reason": "Not evaluated",
			"relevancy_score": 0.0,
			"relevancy_reason": "Not evaluated",
			"correctness_score": 0.0,
			"correctness_reason": "Not evaluated",
			"similarity_score": 0.0,
			"similarity_reason": "Not evaluated"
		}
		
		# Run evaluations with individual error handling
		try:
			faithfulness_result = faithfulness_evaluator.evaluate_response(
				query=query,
				response=response_obj
			)
			scores["faithfulness_score"] = faithfulness_result.score
			scores["faithfulness_reason"] = faithfulness_result.feedback
			print(f"  ✅ Faithfulness: {faithfulness_result.score:.2f}")
		except Exception as e:
			print(f"  ❌ Faithfulness evaluation failed: {e}")
			scores["faithfulness_reason"] = f"Evaluation error: {e}"

		try:
			relevancy_result = relevancy_evaluator.evaluate_response(
				query=query,
				response=response_obj
			)
			scores["relevancy_score"] = relevancy_result.score
			scores["relevancy_reason"] = relevancy_result.feedback
			print(f"  ✅ Relevancy: {relevancy_result.score:.2f}")
		except Exception as e:
			print(f"  ❌ Relevancy evaluation failed: {e}")
			scores["relevancy_reason"] = f"Evaluation error: {e}"

		try:
			correctness_result = correctness_evaluator.evaluate_response(
				query=query,
				response=response_obj,
				reference=expected
			)
			scores["correctness_score"] = correctness_result.score
			scores["correctness_reason"] = correctness_result.feedback
			print(f"  ✅ Correctness: {correctness_result.score:.2f}")
		except Exception as e:
			print(f"  ❌ Correctness evaluation failed: {e}")
			scores["correctness_reason"] = f"Evaluation error: {e}"

		try:
			similarity_result = similarity_evaluator.evaluate_response(
				query=query,
				response=response_obj,
				reference=expected
			)
			scores["similarity_score"] = similarity_result.score
			scores["similarity_reason"] = similarity_result.feedback
			print(f"  ✅ Similarity: {similarity_result.score:.2f}")
		except Exception as e:
			print(f"  ❌ Similarity evaluation failed: {e}")
			scores["similarity_reason"] = f"Evaluation error: {e}"

		# Normalize correctness score to 0-1 scale for fair comparison
		normalized_correctness = (scores["correctness_score"] - 1.0) / 4.0 if scores["correctness_score"] > 0 else 0.0
		
		# Calculate composite score with normalized values
		valid_scores = [s for s in [
			scores["faithfulness_score"],
			scores["relevancy_score"], 
			normalized_correctness,
			scores["similarity_score"]
		] if s > 0]
		
		composite_score = sum(valid_scores) / len(valid_scores) if valid_scores else 0.0

		# Extract source information
		source_details = []
		for j, source in enumerate(sources):
			source_info = {
				f"source_{j+1}_score": source.get('score', 0.0),
				f"source_{j+1}_file": source.get('metadata', {}).get('file_name', 'Unknown'),
				f"source_{j+1}_page": source.get('metadata', {}).get('page_label', 'Unknown'),
				f"source_{j+1}_text_preview": source.get('text', '')[:200] + "..." if len(source.get('text', '')) > 200 else source.get('text', '')
			}
			source_details.append(source_info)

		# Flatten source details into main result
		flattened_sources = {}
		for source_info in source_details:
			flattened_sources.update(source_info)

		# Store comprehensive results
		result = {
			"question_id": i + 1,
			"query": query,
			"query_length": len(query),
			"expected_answer": expected,
			"expected_answer_length": len(expected),
			"model_answer": response_text,
			"model_answer_length": len(response_text),
			
			# Scores (original and normalized)
			"composite_score": composite_score,
			"faithfulness_score": scores["faithfulness_score"],
			"relevancy_score": scores["relevancy_score"], 
			"correctness_score": scores["correctness_score"],
			"correctness_score_normalized": normalized_correctness,
			"similarity_score": scores["similarity_score"],
			
			# Feedback/Reasoning
			"faithfulness_feedback": scores["faithfulness_reason"],
			"relevancy_feedback": scores["relevancy_reason"],
			"correctness_feedback": scores["correctness_reason"], 
			"similarity_feedback": scores["similarity_reason"],
			
			# Performance metrics
			"response_time_seconds": response_time,
			"num_sources_retrieved": len(sources),
			"avg_source_score": sum(s.get('score', 0) for s in sources) / len(sources) if sources else 0.0,
			"max_source_score": max(s.get('score', 0) for s in sources) if sources else 0.0,
			"min_source_score": min(s.get('score', 0) for s in sources) if sources else 0.0,
			
			# Content analysis
			"response_word_count": len(response_text.split()),
			"query_word_count": len(query.split()),
			"expected_word_count": len(expected.split()),
			"response_sentence_count": len([s for s in response_text.split('.') if s.strip()]),
			
			# Source file diversity
			"unique_source_files": len(set(s.get('metadata', {}).get('file_name', '') for s in sources)),
			"source_files": ", ".join(set(s.get('metadata', {}).get('file_name', 'Unknown') for s in sources)),
			
			**flattened_sources
		}
		
		results.append(result)
		print(f"  🎯 Composite: {composite_score:.3f} | Faithfulness: {scores['faithfulness_score']:.3f} | Relevancy: {scores['relevancy_score']:.3f} | Correctness: {scores['correctness_score']:.1f}/5.0 | Similarity: {scores['similarity_score']:.3f}")
		print(f"  ⏱️  Time: {response_time:.2f}s | Sources: {len(sources)} | Files: {len(set(s.get('metadata', {}).get('file_name', '') for s in sources))}")

	except Exception as e:
		print(f"  ⚠️ Q{i+1} failed with error: {e}")
		import traceback
		traceback.print_exc()
		
		results.append({
			"question_id": i + 1,
			"query": query,
			"query_length": len(query),
			"expected_answer": expected,
			"expected_answer_length": len(expected),
			"model_answer": "",
			"model_answer_length": 0,
			"composite_score": 0.0,
			"faithfulness_score": 0.0,
			"relevancy_score": 0.0,
			"correctness_score": 0.0,
			"correctness_score_normalized": 0.0,
			"similarity_score": 0.0,
			"faithfulness_feedback": f"Pipeline error: {e}",
			"relevancy_feedback": f"Pipeline error: {e}",
			"correctness_feedback": f"Pipeline error: {e}",
			"similarity_feedback": f"Pipeline error: {e}",
			"response_time_seconds": 0.0,
			"num_sources_retrieved": 0,
			"avg_source_score": 0.0,
			"max_source_score": 0.0,
			"min_source_score": 0.0,
			"response_word_count": 0,
			"query_word_count": len(query.split()),
			"expected_word_count": len(expected.split()),
			"response_sentence_count": 0,
			"unique_source_files": 0,
			"source_files": ""
		})

# === Save results ===
results_df = pd.DataFrame(results)
results_df.to_csv("rag_evaluation_detailed_results.csv", index=False)

# === Print detailed summary statistics ===
print("\n" + "="*80)
print("📊 COMPREHENSIVE EVALUATION SUMMARY")
print("="*80)
print(f"📈 Total questions processed: {len(results_df)}")
print(f"📈 Average composite score: {results_df['composite_score'].mean():.3f} ± {results_df['composite_score'].std():.3f}")
print(f"📈 Score distribution: Min={results_df['composite_score'].min():.3f}, Max={results_df['composite_score'].max():.3f}")

print(f"\n🎯 INDIVIDUAL EVALUATOR SCORES:")
print(f"   Faithfulness:     {results_df['faithfulness_score'].mean():.3f} ± {results_df['faithfulness_score'].std():.3f} (scale: 0.0-1.0)")
print(f"   Relevancy:        {results_df['relevancy_score'].mean():.3f} ± {results_df['relevancy_score'].std():.3f} (scale: 0.0-1.0)")  
print(f"   Correctness:      {results_df['correctness_score'].mean():.3f} ± {results_df['correctness_score'].std():.3f} (scale: 1.0-5.0)")
print(f"   Correctness (norm): {results_df['correctness_score_normalized'].mean():.3f} ± {results_df['correctness_score_normalized'].std():.3f} (scale: 0.0-1.0)")
print(f"   Similarity:       {results_df['similarity_score'].mean():.3f} ± {results_df['similarity_score'].std():.3f} (scale: 0.0-1.0)")

print(f"\n⚡ PERFORMANCE METRICS:")
print(f"   Avg response time:     {results_df['response_time_seconds'].mean():.2f}s ± {results_df['response_time_seconds'].std():.2f}s")
print(f"   Avg sources retrieved: {results_df['num_sources_retrieved'].mean():.1f} ± {results_df['num_sources_retrieved'].std():.1f}")
print(f"   Avg unique files used: {results_df['unique_source_files'].mean():.1f} ± {results_df['unique_source_files'].std():.1f}")
print(f"   Avg source score:      {results_df['avg_source_score'].mean():.3f} ± {results_df['avg_source_score'].std():.3f}")

print(f"\n� CONTENT ANALYSIS:")
print(f"   Avg response length:   {results_df['model_answer_length'].mean():.0f} chars ± {results_df['model_answer_length'].std():.0f}")
print(f"   Avg response words:    {results_df['response_word_count'].mean():.0f} ± {results_df['response_word_count'].std():.0f}")
print(f"   Avg response sentences: {results_df['response_sentence_count'].mean():.1f} ± {results_df['response_sentence_count'].std():.1f}")
print(f"   Avg query length:      {results_df['query_length'].mean():.0f} chars")

print(f"\n TOP PERFORMING QUESTIONS (by composite score):")
top_questions = results_df.nlargest(3, 'composite_score')[['question_id', 'composite_score', 'query']]
for _, row in top_questions.iterrows():
	print(f"   Q{row['question_id']:2d} (Score: {row['composite_score']:.3f}): {row['query'][:60]}...")

print(f"\n🔻 LOWEST PERFORMING QUESTIONS (by composite score):")
bottom_questions = results_df.nsmallest(3, 'composite_score')[['question_id', 'composite_score', 'query']]
for _, row in bottom_questions.iterrows():
	print(f"   Q{row['question_id']:2d} (Score: {row['composite_score']:.3f}): {row['query'][:60]}...")

# === Enhanced summary statistics ===
enhanced_summary = {
	"evaluation_timestamp": pd.Timestamp.now().isoformat(),
	"total_questions": len(results_df),
	"successful_evaluations": len(results_df[results_df['composite_score'] > 0]),
	"failed_evaluations": len(results_df[results_df['composite_score'] == 0]),
	
	# Score statistics
	"avg_composite_score": results_df['composite_score'].mean(),
	"std_composite_score": results_df['composite_score'].std(),
	"min_composite_score": results_df['composite_score'].min(),
	"max_composite_score": results_df['composite_score'].max(),
	"median_composite_score": results_df['composite_score'].median(),
	
	# Individual evaluator averages
	"avg_faithfulness_score": results_df['faithfulness_score'].mean(),
	"avg_relevancy_score": results_df['relevancy_score'].mean(),
	"avg_correctness_score": results_df['correctness_score'].mean(),
	"avg_correctness_score_normalized": results_df['correctness_score_normalized'].mean(),
	"avg_similarity_score": results_df['similarity_score'].mean(),
	
	# Performance metrics
	"avg_response_time": results_df['response_time_seconds'].mean(),
	"total_evaluation_time": results_df['response_time_seconds'].sum(),
	"avg_sources_retrieved": results_df['num_sources_retrieved'].mean(),
	"avg_unique_files_used": results_df['unique_source_files'].mean(),
	"avg_source_relevance_score": results_df['avg_source_score'].mean(),
	
	# Content statistics
	"avg_response_length": results_df['model_answer_length'].mean(),
	"avg_response_words": results_df['response_word_count'].mean(),
	"avg_query_length": results_df['query_length'].mean()
}

enhanced_summary_df = pd.DataFrame([enhanced_summary])
enhanced_summary_df.to_csv("rag_evaluation_enhanced_summary.csv", index=False)

print(f"\n📊 Results saved to:")
print(f"  • rag_evaluation_detailed_results.csv       (Complete detailed results)")
print(f"  • rag_evaluation_enhanced_summary.csv       (Comprehensive summary statistics)")
print("="*80)