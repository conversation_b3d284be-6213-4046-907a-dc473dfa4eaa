from dotenv import load_dotenv
import os
from core.ragPipeline import AdvancedRAGPipeline

load_dotenv()

def main():

	# Check for API key
	openai_api_key = os.getenv("OPENAI_API_KEY")
	if not openai_api_key:
		print("❌ Error: OPENAI_API_KEY not found in environment variables")
		print("   Please add your API key to a .env file")
		return
	
	print("🚀 Initializing RAG Pipeline...")
	
	# Initialize advanced pipeline for PDFs
	try:
		rag = AdvancedRAGPipeline(
			persist_dir="./rag_storage",
		)
	except Exception as e:
		print(f"❌ Error initializing RAG Pipeline: {e}")
		return
	
	# Create or load index
	try:
		if not rag.load_existing_index():
			pdf_directory = "./data"
			if not os.path.exists(pdf_directory):
				print(f"❌ Error: Data directory '{pdf_directory}' not found")
				print("   Please create the directory and add your PDF files")
				return
			
			print(f"📁 Creating new index from: {pdf_directory}")
			rag.create_index_from_pdf_directory(pdf_directory)
		
		# Setup chat engine
		rag.setup_chat_engine()
		
	except Exception as e:
		print(f"❌ Error setting up RAG pipeline: {e}")
		return
	
	# Display configuration
	config = rag.get_pipeline_config()
	print("\n📋 Pipeline Configuration:")
	for key, value in config.items():
		print(f"   {key}: {value}")
	
	# Start chat interface
	print("\n🤖 RAG Chat Engine Ready!")
	print("━" * 50)
	print("💡 Commands:")
	print("   • Ask any question about your documents")
	print("   • Type 'reset' to clear chat history")
	print("   • Type 'config' to show configuration")
	print("   • Type 'exit' to quit")
	print("━" * 50)
	
	# Interactive chat loop
	while True:
		try:
			message = input("\n💬 You: ").strip()
			
			if not message:
				continue
			elif message.lower() == 'exit':
				print("👋 Goodbye!")
				break
			elif message.lower() == 'reset':
				rag.reset_chat()
				continue
			elif message.lower() == 'config':
				config = rag.get_pipeline_config()
				print("\n📋 Current Configuration:")
				for key, value in config.items():
					print(f"   {key}: {value}")
				continue
			
			# Process message
			print("\n" + "═" * 60)
			result = rag.chat(message, verbose=True)
			print(f"\n🤖 Assistant: {result['response']}")
			print("═" * 60)
			
		except KeyboardInterrupt:
			print("\n\n👋 Chat interrupted. Goodbye!")
			break
		except Exception as e:
			print(f"\n❌ Error: {e}")

if __name__ == "__main__":
	main()