"""
LangWatch Configuration Module

This module handles the initialization and configuration of LangWatch
for tracking RAG pipeline queries, responses, and execution traces.
"""

import os
import logging
from typing import Optional, Dict, Any
from datetime import datetime

try:
    import langwatch
    LANGWATCH_AVAILABLE = True
except ImportError:
    LANGWATCH_AVAILABLE = False
    langwatch = None

logger = logging.getLogger(__name__)


class LangWatchConfig:
    """Configuration and management class for LangWatch integration"""
    
    def __init__(self, 
                 api_key: Optional[str] = None,
                 project_name: Optional[str] = None,
                 version: Optional[str] = None,
                 user_id: Optional[str] = None,
                 enabled: bool = True):
        """
        Initialize LangWatch configuration
        
        Args:
            api_key: LangWatch API key (defaults to LANGWATCH_API_KEY env var)
            project_name: Project name for tagging (defaults to LANGWATCH_PROJECT_NAME env var)
            version: Version tag (defaults to LANGWATCH_VERSION env var)
            user_id: User ID for session tracking (defaults to LANGWATCH_USER_ID env var)
            enabled: Whether to enable LangWatch tracking (defaults to LANGWATCH_ENABLED env var)
        """
        self.enabled = enabled and LANGWATCH_AVAILABLE
        self.api_key = api_key or os.getenv("LANGWATCH_API_KEY")
        self.project_name = project_name or os.getenv("LANGWATCH_PROJECT_NAME", "rag-policy-pipeline")
        self.version = version or os.getenv("LANGWATCH_VERSION", "1.0.0")
        self.user_id = user_id or os.getenv("LANGWATCH_USER_ID")
        self.initialized = False
        
        # Check if LangWatch should be enabled based on environment
        env_enabled = os.getenv("LANGWATCH_ENABLED", "true").lower() == "true"
        self.enabled = self.enabled and env_enabled
        
        if not LANGWATCH_AVAILABLE and self.enabled:
            logger.warning("LangWatch is not available. Install with: pip install langwatch")
            self.enabled = False
        
        if self.enabled and not self.api_key:
            logger.warning("LangWatch API key not found. Set LANGWATCH_API_KEY environment variable.")
            self.enabled = False
    
    def initialize(self) -> bool:
        """
        Initialize LangWatch with the configured settings

        Returns:
            bool: True if initialization was successful, False otherwise
        """
        if not self.enabled:
            logger.info("LangWatch is disabled or not available")
            return False

        if self.initialized:
            logger.debug("LangWatch already initialized")
            return True

        try:
            # Prepare base attributes for LangWatch
            base_attributes = {}
            if self.project_name:
                base_attributes["service.name"] = self.project_name
            if self.version:
                base_attributes["service.version"] = self.version
            if self.user_id:
                base_attributes["user.id"] = self.user_id

            langwatch.setup(
                api_key=self.api_key,
                base_attributes=base_attributes if base_attributes else None,
                debug=False
            )

            self.initialized = True
            logger.info("LangWatch initialized successfully")
            logger.info(f"LangWatch base attributes: {base_attributes}")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize LangWatch: {e}")
            self.enabled = False
            return False
    
    def set_session_context(self, session_id: Optional[str] = None,
                          user_id: Optional[str] = None,
                          additional_context: Optional[Dict[str, Any]] = None) -> None:
        """
        Set session-specific context for LangWatch tracking

        Args:
            session_id: Session identifier
            user_id: User identifier for this session
            additional_context: Additional context data
        """
        if not self.enabled or not self.initialized:
            return

        try:
            self._session_context = {}
            if session_id:
                self._session_context["session_id"] = session_id
            if user_id:
                self._session_context["user_id"] = user_id
            if additional_context:
                self._session_context.update(additional_context)

            logger.debug(f"LangWatch session context updated: {self._session_context}")

        except Exception as e:
            logger.error(f"Failed to set LangWatch session context: {e}")

    def get_session_context(self) -> Dict[str, Any]:
        """Get the current session context"""
        return getattr(self, '_session_context', {})
    
    def track_query_start(self, query: str, metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Track the start of a query using LangWatch trace decorator

        Args:
            query: The user query
            metadata: Additional metadata for the query

        Returns:
            Optional[str]: Trace ID if tracking is enabled, None otherwise
        """
        if not self.enabled or not self.initialized:
            return None

        try:
            trace_metadata = {}
            if metadata:
                trace_metadata.update(metadata)

            session_context = self.get_session_context()
            if session_context:
                trace_metadata.update(session_context)

            self._current_trace_metadata = trace_metadata

            trace_id = f"trace_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            logger.debug(f"Query tracking prepared: {trace_id}")
            return trace_id

        except Exception as e:
            logger.error(f"Failed to prepare query tracking: {e}")
            return None

    def track_query_end(self, trace_id: Optional[str], response: str,
                       sources: list, metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Track the end of a query with response and sources

        Args:
            trace_id: Trace identifier from track_query_start
            response: The generated response
            sources: Retrieved document sources
            metadata: Additional metadata
        """
        if not self.enabled or not self.initialized:
            return

        try:
            logger.debug(f"Query tracking completed: {trace_id}")

        except Exception as e:
            logger.error(f"Failed to complete query tracking: {e}")

    def get_current_trace_metadata(self) -> Dict[str, Any]:
        """Get metadata for the current trace"""
        return getattr(self, '_current_trace_metadata', {})
    
    def is_enabled(self) -> bool:
        """Check if LangWatch tracking is enabled and initialized"""
        return self.enabled and self.initialized
    
    def get_status(self) -> Dict[str, Any]:
        """Get the current status of LangWatch configuration"""
        return {
            "available": LANGWATCH_AVAILABLE,
            "enabled": self.enabled,
            "initialized": self.initialized,
            "api_key_configured": bool(self.api_key),
            "project_name": self.project_name,
            "version": self.version,
            "user_id": self.user_id
        }


# Global instance for easy access
_langwatch_config = None


def get_langwatch_config() -> LangWatchConfig:
    """Get the global LangWatch configuration instance"""
    global _langwatch_config
    if _langwatch_config is None:
        _langwatch_config = LangWatchConfig()
    return _langwatch_config


def initialize_langwatch(api_key: Optional[str] = None,
                        project_name: Optional[str] = None,
                        version: Optional[str] = None,
                        user_id: Optional[str] = None,
                        enabled: bool = True) -> bool:
    """
    Initialize LangWatch with the provided configuration
    
    Args:
        api_key: LangWatch API key
        project_name: Project name for tagging
        version: Version tag
        user_id: User ID for session tracking
        enabled: Whether to enable LangWatch tracking
        
    Returns:
        bool: True if initialization was successful, False otherwise
    """
    global _langwatch_config
    _langwatch_config = LangWatchConfig(
        api_key=api_key,
        project_name=project_name,
        version=version,
        user_id=user_id,
        enabled=enabled
    )
    return _langwatch_config.initialize()
