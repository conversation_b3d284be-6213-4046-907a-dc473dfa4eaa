import os
import re
import logging
from typing import List, Optional, Union, Dict, Any
from pathlib import Path
from datetime import datetime
from llama_index.core import (
	VectorStoreIndex,
	Settings,
	StorageContext,
	load_index_from_storage,
	SimpleDirectoryReader
)
from llama_index.core.node_parser import Sen<PERSON>ceSplitter
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.response_synthesizers import get_response_synthesizer
from llama_index.vector_stores.chroma import ChromaVectorStore
from llama_index.core.postprocessor import SentenceTransformerRerank
from llama_index.core.chat_engine import CondensePlusContextChatEngine
from llama_index.core.prompts import PromptTemplate

import chromadb
from .langwatch_config import get_langwatch_config, initialize_langwatch

# Set up logging
logger = logging.getLogger(__name__)

class AdvancedRAGPipeline:
	def __init__(self,
				 persist_dir: str = "./storage",
				 collection_name: str = "rag_collection",
				 use_reranker: bool = True,
				 reranker_model: str = "BAAI/bge-reranker-base",
				 reranker_device: str = "cpu",
				 llm_model: str = "gpt-4o",
				 embedding_model: str = "text-embedding-3-small",
				 chunk_size: int = 500,
				 chunk_overlap: int = 50,
				 reranker_top_n: int = 5,
				 enable_langwatch: bool = True,
				 langwatch_api_key: Optional[str] = None,
				 langwatch_project_name: Optional[str] = None,
				 langwatch_version: Optional[str] = None,
				 langwatch_user_id: Optional[str] = None):
		"""
		Initialize Advanced RAG Pipeline with LlamaIndex, Reranker, and LangWatch tracking

		Args:
			persist_dir: Directory to persist the index
			collection_name: Name for the vector store collection
			use_reranker: Whether to use reranking
			reranker_model: Model for reranking
			reranker_device: Device to use for reranker (cpu/cuda)
			llm_model: LLM model to use for responses
			embedding_model: Embedding model to use
			chunk_size: Size of text chunks
			chunk_overlap: Overlap between chunks
			reranker_top_n: Number of documents to keep after reranking
			enable_langwatch: Whether to enable LangWatch tracking
			langwatch_api_key: LangWatch API key (overrides env var)
			langwatch_project_name: Project name for LangWatch (overrides env var)
			langwatch_version: Version tag for LangWatch (overrides env var)
			langwatch_user_id: User ID for LangWatch (overrides env var)
		"""

		# Set up LLM and other configurations
		self.llm = llm_model
		self.persist_dir = persist_dir
		self.collection_name = collection_name
		self.use_reranker = use_reranker
		self.reranker_model = reranker_model
		self.reranker_device = reranker_device
		self.reranker_top_n = reranker_top_n
		self.chunk_size = chunk_size
		self.chunk_overlap = chunk_overlap
		self.embedding_model = embedding_model
		self.index = None

		# Initialize LangWatch tracking
		self.langwatch_config = None
		if enable_langwatch:
			try:
				self.langwatch_config = get_langwatch_config()
				# Override with provided parameters if specified
				if langwatch_api_key or langwatch_project_name or langwatch_version or langwatch_user_id:
					initialize_langwatch(
						api_key=langwatch_api_key,
						project_name=langwatch_project_name,
						version=langwatch_version,
						user_id=langwatch_user_id,
						enabled=enable_langwatch
					)
					self.langwatch_config = get_langwatch_config()
				else:
					self.langwatch_config.initialize()

				if self.langwatch_config.is_enabled():
					logger.info("LangWatch tracking enabled for RAG pipeline")
				else:
					logger.info("LangWatch tracking disabled or not available")
			except Exception as e:
				logger.warning(f"Failed to initialize LangWatch: {e}")
				self.langwatch_config = None
		
		# Configure LlamaIndex settings
		Settings.embed_model = OpenAIEmbedding(
			model=self.embedding_model,
		)
		Settings.llm = OpenAI(model=self.llm, temperature=0.1)
		
		# Initialize text splitter for chunking
		# Using SentenceSplitter for paragraph-based chunking
		# This will split text into chunks of 500 characters with 50 character overlap
		# and use double newlines as paragraph separators
		Settings.node_parser = SentenceSplitter(
			chunk_size=self.chunk_size,
			chunk_overlap=self.chunk_overlap,
			paragraph_separator="\n\n"
		)
		
		
		# Initialize ChromaDB
		try:
			self.chroma_client = chromadb.PersistentClient(path=persist_dir)
		except Exception as e:
			raise ValueError(f"Failed to initialize ChromaDB client: {e}")
		
		# Initialize reranker if enabled
		self.reranker = None
		if self.use_reranker:
			try:
				# Use specified device for reranker (cpu/cuda)
				self.reranker = SentenceTransformerRerank(
					model=self.reranker_model,
					top_n=self.reranker_top_n,
					device=self.reranker_device
				)
				print(f"Initialized reranker: {self.reranker_model} on {self.reranker_device}")
			except Exception as e:
				print(f"Warning: Could not initialize reranker: {e}")
				self.use_reranker = False
		
	def create_index_from_pdf_directory(self, directory_path: str,
									   recursive: bool = False) -> None:
		"""
		Create vector index from all PDFs in a directory
		
		Args:
			directory_path: Path to directory containing PDFs
			recursive: Whether to search subdirectories
		"""
		if not os.path.exists(directory_path):
			raise ValueError(f"Directory {directory_path} does not exist")
		print(f"📁 Loading PDFs from directory: {directory_path}")
		
		# Use SimpleDirectoryReader for directory processing
		reader = SimpleDirectoryReader(
			input_dir=directory_path,
			required_exts=[".pdf"],
			recursive=recursive,
			filename_as_id=True
		)
		
		try:
			documents = reader.load_data()
			
			if not documents:
				raise ValueError(f"No PDF documents found in {directory_path}")
			
			# Add metadata
			for doc in documents:
				doc.metadata["file_type"] = "pdf"
				# SimpleDirectoryReader already adds file_path metadata
			
			print(f"Loaded {len(documents)} pages from PDF files")
			
			# Create ChromaDB collection
			chroma_collection = self.chroma_client.get_or_create_collection(self.collection_name)
			vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
			storage_context = StorageContext.from_defaults(vector_store=vector_store)
			
			# Create index
			self.index = VectorStoreIndex.from_documents(
				documents, 
				storage_context=storage_context
			)
			
			# Persist index
			self.index.storage_context.persist(persist_dir=self.persist_dir)
			
			print(f"Created and persisted index with {len(documents)} pages from directory")
			
		except Exception as e:
			raise ValueError(f"Error loading PDFs from directory {directory_path}: {e}")

		
	def load_existing_index(self) -> bool:
		"""
		Load existing index from storage
		
		Returns:
			bool: True if index loaded successfully, False otherwise
		"""
		try:
			# Rebuild storage context with ChromaDB
			chroma_collection = self.chroma_client.get_collection(self.collection_name)
			vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
			storage_context = StorageContext.from_defaults(
				vector_store=vector_store,
				persist_dir=self.persist_dir
			)
			
			# Load index
			self.index = load_index_from_storage(storage_context)
			print("Loaded existing index successfully")
			return True
		except Exception as e:
			print(f"Could not load existing index: {e}")
			return False
	
	def get_custom_qa_prompt(self) -> PromptTemplate:
		"""
		Create custom QA prompt template with dynamic placeholders
		"""
		template_str = """You are a helpful and accurate university policy assistant for um6p.

You must never refer to yourself as ChatGPT, GPT-4, or any technical model name. Always identify as a UM6P policy assistant.

Your task is to answer questions strictly based on retrieved chunks from official UM6P policy documents. Do not make up information. Only respond using what is explicitly found in the chunks.

Always cite your sources using the required markdown format. If the answer cannot be found in the chunks, respond with: "Sorry I don't know."

Your tone should be clear, formal, and professional.

You are tasked with answering a question using provided chunks of information. Your goal is to provide an accurate answer while citing your sources using a specific markdown format.

Here is the question you need to answer:
<question>
{query_str}
</question>

Below are chunks of information that you can use to answer the question.

<chunks>
{context_str}
</chunks>

Your task is to answer the question using the information provided in these chunks.
When you use information from a specific chunk in your answer, you must cite it using this exact format: [Source X](chunk_X) where X is the chunk number.
The citation should appear at the end of the sentence where the information is used.

For example:
- "According to the policy, students must follow guidelines [Source 1](chunk_1)."
- "The university requires transparency in AI usage [Source 2](chunk_2)."

If you cannot answer the question using the provided chunks, say:
**"Sorry I don't know."**

You are a helpful and accurate university policy assistant for um6p.
"""

		return PromptTemplate(template_str)
	
	def get_custom_refine_prompt(self) -> PromptTemplate:
		"""
		Create custom refine prompt for multi-step responses
		"""
		refine_system_content = """You are a helpful and accurate university policy assistant for um6p.

You must never refer to yourself as ChatGPT, GPT-4, or any technical model name. Always identify as a UM6P policy assistant.

The original question was: {query_str}

We have provided an existing answer: {existing_answer}

We have the opportunity to refine the existing answer (only if needed) with some more context below.
{context_msg}

Given the new context, refine the original answer to better answer the question. 
If the context isn't useful, return the original answer.


If you cannot answer the question using the provided chunks, say:
**"Sorry I don't know."**
You are a helpful and accurate university policy assistant for um6p."""

		return PromptTemplate(refine_system_content)
	
	def get_system_prompt(self) -> str:
		return """You are a helpful and accurate university policy assistant for um6p.

You must never refer to yourself as ChatGPT, GPT-4, or any technical model name. Always identify as a UM6P policy assistant.

Your task is to answer questions strictly based on retrieved chunks from official UM6P policy documents. Do not make up information. Only respond using what is explicitly found in the chunks.

Always cite your sources using the required markdown format. If the answer cannot be found in the chunks, respond with: "Sorry I don't know."

Your tone should be clear, formal, and professional.

You are tasked with answering a question using provided chunks of information. Your goal is to provide an accurate answer while citing your sources using a specific markdown format.

Here is the question you need to answer:
<question>
{query_str}
</question>

Below are chunks of information that you can use to answer the question.

<chunks>
{context_str}
</chunks>

Your task is to answer the question using the information provided in these chunks.
When you use information from a specific chunk in your answer, you must cite it using this exact format: [Source X](chunk_X) where X is the chunk number.
The citation should appear at the end of the sentence where the information is used.

For example:
- "According to the policy, students must follow guidelines [Source 1](chunk_1)."
- "The university requires transparency in AI usage [Source 2](chunk_2)."

If you cannot answer the question using the provided chunks, say:
**"Sorry I don't know."**

You are a helpful and accurate university policy assistant for um6p."""

	def setup_chat_engine(self, 
					 similarity_top_k: int = 10,
					 response_mode: str = "compact",
					 chat_mode: str = "condense_plus_context") -> None:
		"""
		Setup chat engine for conversational RAG
		
		Args:
			similarity_top_k: Number of top similar chunks to retrieve
			response_mode: Response synthesis mode ('compact', 'tree_summarize', 'simple_summarize', etc.)
			chat_mode: Type of chat engine
		"""
		if self.index is None:
			raise ValueError("Index not created or loaded. Call create_index_from_documents() or load_existing_index() first.")
		
		# Configure retriever
		retriever = VectorIndexRetriever(
			index=self.index,
			similarity_top_k=similarity_top_k,
		)
		
		custom_qa_prompt = self.get_custom_qa_prompt()
		custom_refine_prompt = self.get_custom_refine_prompt()

		# Configure response synthesizer
		response_synthesizer = get_response_synthesizer(
			response_mode=response_mode,
			text_qa_template=custom_qa_prompt,
			refine_template=custom_refine_prompt,
		)
		
		# Add reranker if enabled
		node_postprocessors = []
		if self.use_reranker and self.reranker:
			node_postprocessors.append(self.reranker)
			print(f"Added reranker (top_n={self.reranker_top_n})")
		
		# Format prompt
		custom_system_prompt = self.get_system_prompt()

		# Create chat engine with explicit response synthesizer
		self.chat_engine = CondensePlusContextChatEngine.from_defaults(
			retriever=retriever,
			response_synthesizer=response_synthesizer,
			node_postprocessors=node_postprocessors,
			system_prompt=custom_system_prompt,
			verbose=False
		)
		
		features = []
		if self.use_reranker: features.append("reranking")
		features.append("conversational context")
		features.append(f"response_mode: {response_mode}")
	
		print(f"Chat engine setup complete with: {', '.join(features)}")
	
	def chat(self, message: str, verbose: bool = False, session_id: Optional[str] = None) -> dict:
		"""
		Chat with the RAG pipeline (supports follow-up questions) with LangWatch tracking

		Args:
			message: The message/question to send
			verbose: Whether to print debug information
			session_id: Optional session ID for LangWatch tracking

		Returns:
			dict: Contains 'response', 'sources', 'metadata', and 'langwatch_trace_id'
		"""
		if not hasattr(self, 'chat_engine') or self.chat_engine is None:
			raise ValueError("Chat engine not setup. Call setup_chat_engine() first.")

		# Prepare LangWatch tracking
		start_time = datetime.now()
		trace_id = None
		if self.langwatch_config and self.langwatch_config.is_enabled():
			try:
				# Set session context if provided
				if session_id:
					self.langwatch_config.set_session_context(session_id=session_id)

				# Prepare trace metadata
				trace_id = self.langwatch_config.track_query_start(
					query=message,
					metadata={
						"timestamp": start_time.isoformat(),
						"session_id": session_id,
						"pipeline_config": {
							"llm_model": self.llm,
							"embedding_model": self.embedding_model,
							"use_reranker": self.use_reranker,
							"reranker_model": self.reranker_model if self.use_reranker else None
						}
					}
				)
			except Exception as e:
				logger.warning(f"LangWatch query preparation failed: {e}")

		# Execute the actual chat with LangWatch tracing if enabled
		if self.langwatch_config and self.langwatch_config.is_enabled():
			return self._chat_with_langwatch_tracing(message, verbose, session_id, start_time, trace_id)
		else:
			return self._chat_without_tracing(message, verbose, session_id, start_time, trace_id)

	def _chat_with_langwatch_tracing(self, message: str, verbose: bool, session_id: Optional[str],
									start_time: datetime, trace_id: Optional[str]) -> dict:
		"""Execute chat with LangWatch tracing enabled"""
		try:
			import langwatch

			# Get trace metadata
			trace_metadata = self.langwatch_config.get_current_trace_metadata()

			# Store response data at class level for access in traced function
			self._current_response_data = None

			@langwatch.trace(name="RAG Chat Query", metadata=trace_metadata)
			def execute_rag_chat(query: str):
				"""Traced function that captures only the query as input and returns response text"""
				# Execute the core chat logic
				response_data = self._execute_chat_core(query, verbose)
				# Store full response data for later use
				self._current_response_data = response_data
				# Return only the response text for LangWatch logging
				return response_data['response']

			# Execute the traced chat with only the query parameter
			execute_rag_chat(message)

			# Get the stored response data
			response_data = self._current_response_data

		except Exception as e:
			logger.warning(f"LangWatch tracing failed, falling back to non-traced execution: {e}")
			response_data = self._execute_chat_core(message, verbose)

		# Complete the response with timing and trace info
		end_time = datetime.now()
		latency_ms = (end_time - start_time).total_seconds() * 1000

		response_data['metadata']['latency_ms'] = latency_ms
		response_data['metadata']['timestamp'] = start_time.isoformat()
		response_data['langwatch_trace_id'] = trace_id

		# End LangWatch tracking
		if self.langwatch_config and self.langwatch_config.is_enabled():
			try:
				self.langwatch_config.track_query_end(
					trace_id=trace_id,
					response=response_data['response'],
					sources=response_data['sources'],
					metadata={
						"end_timestamp": end_time.isoformat(),
						"latency_ms": latency_ms,
						"num_sources_retrieved": response_data['metadata']['total_retrieved'],
						"num_sources_cited": response_data['metadata']['num_sources'],
						"session_id": session_id
					}
				)
			except Exception as e:
				logger.warning(f"LangWatch query end tracking failed: {e}")

		return response_data

	def _chat_without_tracing(self, message: str, verbose: bool, session_id: Optional[str],
							 start_time: datetime, trace_id: Optional[str]) -> dict:
		"""Execute chat without LangWatch tracing"""
		response_data = self._execute_chat_core(message, verbose)

		# Add timing and trace info
		end_time = datetime.now()
		latency_ms = (end_time - start_time).total_seconds() * 1000

		response_data['metadata']['latency_ms'] = latency_ms
		response_data['metadata']['timestamp'] = start_time.isoformat()
		response_data['langwatch_trace_id'] = trace_id

		return response_data

	def _execute_chat_core(self, message: str, verbose: bool) -> dict:
		"""Core chat execution logic"""
		if verbose:
			print(f"User message: {message}")

		# Chat with context awareness - add span for LangWatch tracking
		if self.langwatch_config and self.langwatch_config.is_enabled():
			try:
				import langwatch

				@langwatch.span(type="llm", name="RAG Query Processing")
				def process_rag_query(query: str):
					"""Process the RAG query with retrieval and generation"""
					return self.chat_engine.chat(query)

				response = process_rag_query(message)
			except Exception as e:
				logger.warning(f"LangWatch span creation failed: {e}")
				response = self.chat_engine.chat(message)
		else:
			response = self.chat_engine.chat(message)

		# Extract all source information with LangWatch span tracking
		if self.langwatch_config and self.langwatch_config.is_enabled():
			try:
				import langwatch

				@langwatch.span(type="rag", name="Source Extraction and Filtering")
				def extract_and_filter_sources(response_obj, verbose_mode: bool):
					"""Extract and filter sources from the response"""
					all_sources = []
					if hasattr(response_obj, 'source_nodes'):
						for i, node in enumerate(response_obj.source_nodes):
							source_info = {
								'chunk_id': i + 1,
								'score': float(getattr(node, 'score', 0.0)),
								'text': node.text[:200] + "..." if len(node.text) > 200 else node.text,
								'metadata': node.metadata
							}
							all_sources.append(source_info)

							if verbose_mode:
								print(f"Source {i+1} (score: {source_info['score']:.3f})")
								file_path = node.metadata.get('file_path', 'Unknown')
								file_name = Path(file_path).name if file_path != 'Unknown' else 'Unknown'
								print(f"  File: {file_name}")
								print(f"  Text: {source_info['text']}")
								print()

					# Filter to only show sources that are actually cited in the response
					response_text = str(response_obj)
					cited_sources = self._extract_cited_sources(response_text, all_sources)

					return all_sources, cited_sources, response_text

				all_sources, cited_sources, response_text = extract_and_filter_sources(response, verbose)

			except Exception as e:
				logger.warning(f"LangWatch source extraction span failed: {e}")
				# Fallback to original logic
				all_sources = []
				if hasattr(response, 'source_nodes'):
					for i, node in enumerate(response.source_nodes):
						source_info = {
							'chunk_id': i + 1,
							'score': float(getattr(node, 'score', 0.0)),
							'text': node.text[:200] + "..." if len(node.text) > 200 else node.text,
							'metadata': node.metadata
						}
						all_sources.append(source_info)

						if verbose:
							print(f"Source {i+1} (score: {source_info['score']:.3f})")
							file_path = node.metadata.get('file_path', 'Unknown')
							file_name = Path(file_path).name if file_path != 'Unknown' else 'Unknown'
							print(f"  File: {file_name}")
							print(f"  Text: {source_info['text']}")
							print()

				response_text = str(response)
				cited_sources = self._extract_cited_sources(response_text, all_sources)
		else:
			# Original logic when LangWatch is disabled
			all_sources = []
			if hasattr(response, 'source_nodes'):
				for i, node in enumerate(response.source_nodes):
					source_info = {
						'chunk_id': i + 1,
						'score': float(getattr(node, 'score', 0.0)),
						'text': node.text[:200] + "..." if len(node.text) > 200 else node.text,
						'metadata': node.metadata
					}
					all_sources.append(source_info)

					if verbose:
						print(f"Source {i+1} (score: {source_info['score']:.3f})")
						file_path = node.metadata.get('file_path', 'Unknown')
						file_name = Path(file_path).name if file_path != 'Unknown' else 'Unknown'
						print(f"  File: {file_name}")
						print(f"  Text: {source_info['text']}")
						print()

			response_text = str(response)
			cited_sources = self._extract_cited_sources(response_text, all_sources)

		if verbose:
			print(f"\nFiltered sources: {len(cited_sources)} out of {len(all_sources)} sources were cited")

		return {
			'response': response_text,
			'sources': cited_sources,
			'response_obj': response,
			'metadata': {
				'num_sources': len(cited_sources),
				'total_retrieved': len(all_sources),
				'message_length': len(message)
			}
		}

	async def chat_stream(self, message: str, verbose: bool = False, session_id: Optional[str] = None):
		"""
		Chat with the RAG pipeline with streaming response and LangWatch tracking

		Args:
			message: The message/question to send
			verbose: Whether to print debug information
			session_id: Optional session ID for LangWatch tracking

		Yields:
			dict: Streaming chunks containing response parts, sources, and metadata
		"""
		if not hasattr(self, 'chat_engine') or self.chat_engine is None:
			raise ValueError("Chat engine not setup. Call setup_chat_engine() first.")

		# Start LangWatch tracking
		start_time = datetime.now()
		trace_id = None
		if self.langwatch_config and self.langwatch_config.is_enabled():
			try:
				# Set session context if provided
				if session_id:
					self.langwatch_config.set_session_context(session_id=session_id)

				# Track query start
				trace_id = self.langwatch_config.track_query_start(
					query=message,
					metadata={
						"timestamp": start_time.isoformat(),
						"session_id": session_id,
						"streaming": True,
						"pipeline_config": {
							"llm_model": self.llm,
							"embedding_model": self.embedding_model,
							"use_reranker": self.use_reranker,
							"reranker_model": self.reranker_model if self.use_reranker else None
						}
					}
				)
			except Exception as e:
				logger.warning(f"LangWatch query start tracking failed: {e}")

		if verbose:
			print(f"User message: {message}")

		# Get streaming response from chat engine
		streaming_response = self.chat_engine.stream_chat(message)

		response_text = ""

		# Stream the response
		for token in streaming_response.response_gen:
			response_text += token
			yield {
				'type': 'response_chunk',
				'chunk': token,
				'partial_response': response_text
			}

		# Extract all source information after streaming is complete
		all_sources = []
		if hasattr(streaming_response, 'source_nodes'):
			for i, node in enumerate(streaming_response.source_nodes):
				source_info = {
					'chunk_id': i + 1,
					'score': float(getattr(node, 'score', 0.0)),
					'text': node.text[:200] + "..." if len(node.text) > 200 else node.text,
					'metadata': node.metadata
				}
				all_sources.append(source_info)

				if verbose:
					print(f"Source {i+1} (score: {source_info['score']:.3f})")
					file_path = node.metadata.get('file_path', 'Unknown')
					file_name = Path(file_path).name if file_path != 'Unknown' else 'Unknown'
					print(f"  File: {file_name}")
					print(f"  Text: {source_info['text']}")
					print()

		# Filter to only show sources that are actually cited in the response
		cited_sources = self._extract_cited_sources(response_text, all_sources)

		if verbose:
			print(f"\nFiltered sources: {len(cited_sources)} out of {len(all_sources)} sources were cited")

		# End LangWatch tracking
		end_time = datetime.now()
		latency_ms = (end_time - start_time).total_seconds() * 1000

		if self.langwatch_config and self.langwatch_config.is_enabled():
			try:
				self.langwatch_config.track_query_end(
					trace_id=trace_id,
					response=response_text,
					sources=cited_sources,
					metadata={
						"end_timestamp": end_time.isoformat(),
						"latency_ms": latency_ms,
						"num_sources_retrieved": len(all_sources),
						"num_sources_cited": len(cited_sources),
						"session_id": session_id,
						"streaming": True
					}
				)
			except Exception as e:
				logger.warning(f"LangWatch query end tracking failed: {e}")

		# Send final response with only cited sources
		yield {
			'type': 'response_complete',
			'response': response_text,
			'sources': cited_sources,
			'metadata': {
				'num_sources': len(cited_sources),
				'total_retrieved': len(all_sources),
				'message_length': len(message),
				'latency_ms': latency_ms,
				'timestamp': start_time.isoformat()
			},
			'langwatch_trace_id': trace_id
		}

	def _extract_cited_sources(self, response_text: str, all_sources: List[dict]) -> List[dict]:
		"""
		Extract only the sources that are actually cited in the LLM response.

		Args:
			response_text: The complete LLM response text
			all_sources: List of all retrieved sources

		Returns:
			List of sources that are actually cited in the response
		"""
		# Pattern to match various citation formats
		citation_patterns = [
			r'\[Source\s+(\d+)\]\(chunk_\d+\)',  # [Source 1](chunk_1)
			r'\[(\d+)\]\(chunk_\d+\)',           # [1](chunk_1)
			r'\[source\]\((\d+)\)',              # [source](2)
			r'\[Source\s+(\d+)\]',               # [Source 1]
			r'\[(\d+)\]',                        # [1]
			r'\[source\s+(\d+)\]',               # [source 1]
		]

		cited_chunk_ids = set()

		# Extract cited chunk IDs from the response
		for pattern in citation_patterns:
			matches = re.findall(pattern, response_text)
			for match in matches:
				try:
					chunk_id = int(match)
					cited_chunk_ids.add(chunk_id)
				except ValueError:
					continue

		# Filter sources to only include cited ones
		cited_sources = []
		for source in all_sources:
			if source['chunk_id'] in cited_chunk_ids:
				cited_sources.append(source)

		# If no citations found, return empty list (don't show any sources)
		return cited_sources

	def reset_chat(self) -> None:
		"""Reset chat conversation history"""
		if hasattr(self, 'chat_engine') and self.chat_engine is not None:
			self.chat_engine.reset()
			print("🔄 Chat history reset.")
		else:
			print("⚠️ No chat engine to reset.")

	def get_pipeline_config(self) -> dict:
		"""
		Get current pipeline configuration including LangWatch status

		Returns:
			dict: Configuration details
		"""
		config = {
			"embedding_model": self.embedding_model,
			"llm_model": self.llm,
			"chunk_size": self.chunk_size,
			"chunk_overlap": self.chunk_overlap,
			"chunking_strategy": "paragraph",
			"reranker_enabled": self.use_reranker,
			"reranker_model": self.reranker_model if self.use_reranker else None,
			"reranker_top_n": self.reranker_top_n if self.use_reranker else None,
			"chat_engine": "CondensePlusContext"
		}

		# Add LangWatch status
		if self.langwatch_config:
			config["langwatch"] = self.langwatch_config.get_status()
		else:
			config["langwatch"] = {"enabled": False, "available": False}

		return config

	def get_langwatch_status(self) -> Dict[str, Any]:
		"""
		Get LangWatch tracking status

		Returns:
			dict: LangWatch status and configuration
		"""
		if self.langwatch_config:
			return self.langwatch_config.get_status()
		else:
			return {"enabled": False, "available": False, "initialized": False}

	def set_langwatch_session_context(self, session_id: Optional[str] = None,
									 user_id: Optional[str] = None,
									 additional_context: Optional[Dict[str, Any]] = None) -> None:
		"""
		Set session-specific context for LangWatch tracking

		Args:
			session_id: Session identifier
			user_id: User identifier for this session
			additional_context: Additional context data
		"""
		if self.langwatch_config and self.langwatch_config.is_enabled():
			self.langwatch_config.set_session_context(
				session_id=session_id,
				user_id=user_id,
				additional_context=additional_context
			)