## React TypeScript Chat Widget Demo

This page demonstrates a floating chat widget component built with React and TypeScript.

The widget will automatically attempt to connect to: ws://localhost:8080

You can customize the WebSocket URL, theme, and position via props.


## Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm run dev
```

3. Build for production:
```bash
npm run build
```

## Usage

### Basic Usage

```tsx
import { ChatWidget } from './components/ChatWidget';

function App() {
  return (
    <div>
      <ChatWidget />
    </div>
  );
}
```

### Advanced Usage with Custom Props

```tsx
import { ChatWidget } from './components/ChatWidget';

function App() {
  return (
    <div>
      <ChatWidget
        wsUrl="ws://localhost:8080"
        username="MyUser"
        title="Customer Support"
        position="bottom-right"
        theme={{
          primaryColor: "#667eea",
          secondaryColor: "#764ba2",
          buttonColor: "#ff6b6b"
        }}
        onConnectionChange={(status) => {
          console.log('Connection status:', status);
        }}
        onMessageReceived={(message) => {
          console.log('New message:', message);
        }}
      />
    </div>
  );
}
```


### Theme Configuration

```tsx
interface ThemeConfig {
  primaryColor?: string;    // Main gradient color
  secondaryColor?: string;  // Secondary gradient color  
  buttonColor?: string;     // Chat button color
}
```

## WebSocket Server

The widget expects a WebSocket server that can handle JSON messages in this format:

```typescript
interface WebSocketMessage {
  type: 'message' | 'join' | 'system';
  username?: string;
  message: string;
  timestamp?: string;
}
```

### Example WebSocket Server (Node.js)

```javascript
const WebSocket = require('ws');
const wss = new WebSocket.Server({ port: 8080 });

wss.on('connection', (ws) => {
  console.log('Client connected');
  
  ws.on('message', (data) => {
    const message = JSON.parse(data);
    
    // Broadcast message to all clients
    wss.clients.forEach((client) => {
      if (client !== ws && client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(message));
      }
    });
  });
  
  ws.on('close', () => {
    console.log('Client disconnected');
  });
});
```

## Component Architecture

```
src/
├── components/
│   ├── ChatWidget.tsx      # Main chat widget component
│   ├── ChatWidget.css      # Styles for the widget
│   ├── ConnectionStatus.tsx # Connection status indicator
│   └── Message.tsx         # Individual message component
├── hooks/
│   └── useWebSocket.ts     # WebSocket connection hook
├── types.ts                # TypeScript type definitions
├── index.ts                # Export entry point
└── main.tsx                # Demo application
```

## Customization

### CSS Custom Properties

The widget uses CSS custom properties for easy theming:

```css
.chat-widget-container {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --button-color: #ff6b6b;
}
```

### Mobile Responsive

The widget automatically adapts to mobile screens:
- On screens < 480px, the widget takes full width
- Button position adjusts for better mobile UX

## License

MIT License
