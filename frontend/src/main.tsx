import React from 'react';
import ReactDOM from 'react-dom/client';
import { ChatWidget } from './components/ChatWidget';

const App: React.FC = () => {
  const [searchTerm, setSearchTerm] = React.useState('');

  const handleSearch = () => {
    if (searchTerm.trim()) {
      console.log('Searching for:', searchTerm);
      // Add search functionality here
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div style={{
      fontFamily: 'Montserrat, sans-serif',
      minHeight: '100vh',
      margin: 0,
      padding: 0
    }}>
      {/* Navigation Bar */}
      <nav style={{
        backgroundColor: '#13183E',
        padding: '1rem 2rem',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        color: 'white',
        borderBottom: '0.5px solid white'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img 
            src="/logo.svg" 
            alt="UM6P Logo" 
            style={{
              height: '36px',
              marginRight: '1rem'
            }}
          />
        </div>
        <div style={{ display: 'flex', gap: '2rem', alignItems: 'center' }}>
          <a 
            href="#" 
            style={{ color: 'white', textDecoration: 'none', fontSize: '12px', fontWeight: '600', transition: 'color 0.3s ease' }}
            onMouseEnter={(e) => e.currentTarget.style.color = '#D7492A'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'white'}
          >
            HOME
          </a>
          <a 
            href="#" 
            style={{ color: 'white', textDecoration: 'none', fontSize: '12px', fontWeight: '600', transition: 'color 0.3s ease' }}
            onMouseEnter={(e) => e.currentTarget.style.color = '#D7492A'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'white'}
          >
            ABOUT US
          </a>
          <a 
            href="#" 
            style={{ color: 'white', textDecoration: 'none', fontSize: '12px', fontWeight: '600', transition: 'color 0.3s ease' }}
            onMouseEnter={(e) => e.currentTarget.style.color = '#D7492A'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'white'}
          >
            POLICIES AND PROCEDURES
          </a>
          <a 
            href="#" 
            style={{ color: 'white', textDecoration: 'none', fontSize: '12px', fontWeight: '600', transition: 'color 0.3s ease' }}
            onMouseEnter={(e) => e.currentTarget.style.color = '#D7492A'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'white'}
          >
            DOCUMENTATION
          </a>
          <a 
            href="#" 
            style={{ color: 'white', textDecoration: 'none', fontSize: '12px', fontWeight: '600', transition: 'color 0.3s ease' }}
            onMouseEnter={(e) => e.currentTarget.style.color = '#D7492A'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'white'}
          >
            FAQ'S
          </a>
          <a 
            href="#" 
            style={{ color: 'white', textDecoration: 'none', fontSize: '12px', fontWeight: '600', transition: 'color 0.3s ease' }}
            onMouseEnter={(e) => e.currentTarget.style.color = '#D7492A'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'white'}
          >
            LOGIN
          </a>
          <a 
            href="#" 
            style={{ color: 'white', textDecoration: 'none', fontSize: '12px', fontWeight: '600', transition: 'color 0.3s ease' }}
            onMouseEnter={(e) => e.currentTarget.style.color = '#D7492A'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'white'}
          >
            CONTACT US
          </a>
        </div>
      </nav>

      {/* Hero Section */}
      <div style={{
        backgroundColor: '#13183E',
        color: 'white',
        textAlign: 'center',
        padding: '4rem 2rem',
        minHeight: '60vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <h1 style={{
          fontSize: '2.75rem',
          fontWeight: 'bold',
          marginBottom: '1rem',
          margin: 0
        }}>
          We're Here To Help
        </h1>
        <p style={{
          fontSize: '1rem',
          marginBottom: '2rem',
          opacity: 0.9
        }}>
          Everything you need to know about all the things
        </p>
        
        {/* Search Bar */}
        <div style={{
          display: 'flex',
          maxWidth: '600px',
          width: '100%',
          marginTop: '2rem'
        }}>
          <input
            type="text"
            placeholder="Have a question? Ask or enter a search term."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={handleKeyPress}
            style={{
              flex: 1,
              padding: '1rem 1.5rem',
              border: 'none',
              fontSize: '14px',
              outline: 'none'
            }}
          />
          <button
            onClick={handleSearch}
            style={{
              backgroundColor: '#D7492A',
              color: 'white',
              border: 'none',
              padding: '1rem 2rem',
              fontSize: '14px',
              fontWeight: 'bold',
              cursor: 'pointer',
              transition: 'background-color 0.3s'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#c23e1f'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#D7492A'}
          >
            SEARCH
          </button>
        </div>
      </div>

      {/* Categories Footer */}
      <div style={{
        backgroundColor: '#D7492A',
        color: 'white',
        padding: '2rem',
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '2rem'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '1.75rem', marginBottom: '0.5rem' }}>📋</div>
          <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1rem' }}>Policies and Procedures</h3>
          <p style={{ margin: 0, opacity: 0.9, fontSize: '14px' }}>89 Articles / 11 Categories</p>
        </div>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '1.75rem', marginBottom: '0.5rem' }}>📁</div>
          <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1rem' }}>Documentation</h3>
          <p style={{ margin: 0, opacity: 0.9, fontSize: '14px' }}>1 Articles / 1 Categories</p>
        </div>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '1.75rem', marginBottom: '0.5rem' }}>❓</div>
          <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1rem' }}>FAQ</h3>
          <p style={{ margin: 0, opacity: 0.9, fontSize: '14px' }}>7 Articles / 2 Categories</p>
        </div>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '1.75rem', marginBottom: '0.5rem' }}>📄</div>
          <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1rem' }}>Post</h3>
          <p style={{ margin: 0, opacity: 0.9, fontSize: '14px' }}>5 Articles / 2 Categories</p>
        </div>
      </div>

      {/* Chat Widget Component */}
      <ChatWidget
        wsUrl="ws://localhost:8000/ws"
        username="DemoUser"
        title="Policies Assistant"
        position="bottom-right"
        theme={{
          primaryColor: "#13183E",
          secondaryColor: "#0F1333",
          buttonColor: "#D7492A"
        }}
        onConnectionChange={(status) => {
          console.log('Connection status changed:', status);
        }}
        onMessageReceived={(message) => {
          console.log('New message received:', message);
        }}
      />
    </div>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
