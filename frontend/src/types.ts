export interface ChatMessage {
  id: string;
  type: 'sent' | 'received' | 'system' | 'rag_response' | 'typing';
  message: string;
  sender?: string;
  timestamp: Date;
  sources?: SourceInfo[];
  metadata?: RAGMetadata;
  isStreaming?: boolean;
  isComplete?: boolean;
}

export interface SourceInfo {
  chunk_id: number;
  score: number;
  text: string;
  metadata: {
    file_path?: string;
    page_label?: string;
    [key: string]: any;
  };
}

export interface RAGMetadata {
  num_sources: number;
  message_length: number;
  [key: string]: any;
}

export interface WebSocketMessage {
  type: 'message' | 'join' | 'system' | 'rag_query' | 'rag_chunk' | 'rag_complete' | 'error' | 'ping' | 'pong';
  username?: string;
  message?: string;
  chunk?: string;
  partial_response?: string;
  sources?: SourceInfo[];
  metadata?: RAGMetadata;
  timestamp?: string;
}

export type ConnectionStatus = 'connecting' | 'connected' | 'error' | 'disconnected';

export interface ChatWidgetProps {
  wsUrl?: string;
  username?: string;
  title?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  theme?: {
    primaryColor?: string;
    secondaryColor?: string;
    buttonColor?: string;
  };
  onConnectionChange?: (status: ConnectionStatus) => void;
  onMessageReceived?: (message: ChatMessage) => void;
}
