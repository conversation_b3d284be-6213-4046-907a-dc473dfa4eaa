import { useState, useEffect, useRef, useCallback } from 'react';
import { ChatMessage, WebSocketMessage, ConnectionStatus, SourceInfo, RAGMetadata } from '../types';

interface UseWebSocketProps {
  url: string;
  username: string;
  onConnectionChange?: (status: ConnectionStatus) => void;
}

export const useWebSocket = ({ url, username, onConnectionChange }: UseWebSocketProps) => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('connecting');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isTyping, setIsTyping] = useState(false);

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const reconnectTimerRef = useRef<NodeJS.Timeout | null>(null);
  const currentStreamingMessageRef = useRef<string | null>(null);
  
  const RECONNECT_INTERVAL = 5000;
  const MAX_RECONNECT_ATTEMPTS = 10;

  const updateConnectionStatus = useCallback((status: ConnectionStatus) => {
    setConnectionStatus(status);
    setIsConnected(status === 'connected');
    onConnectionChange?.(status);
  }, [onConnectionChange]);

  const addMessage = useCallback((
    type: ChatMessage['type'],
    message: string,
    sender?: string,
    sources?: SourceInfo[],
    metadata?: RAGMetadata,
    isStreaming?: boolean,
    isComplete?: boolean
  ) => {
    const newMessage: ChatMessage = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
      type,
      message,
      sender,
      timestamp: new Date(),
      sources,
      metadata,
      isStreaming,
      isComplete
    };

    setMessages(prev => [...prev, newMessage]);
    return newMessage;
  }, []);

  const updateStreamingMessage = useCallback((messageId: string, chunk: string, isComplete: boolean = false, sources?: SourceInfo[], metadata?: RAGMetadata) => {
    setMessages(prev => prev.map(msg => {
      if (msg.id === messageId) {
        return {
          ...msg,
          message: msg.message + chunk,
          isStreaming: !isComplete,
          isComplete,
          sources: isComplete ? sources : msg.sources,
          metadata: isComplete ? metadata : msg.metadata
        };
      }
      return msg;
    }));
  }, []);

  const scheduleReconnect = useCallback(() => {
    if (reconnectAttemptsRef.current >= MAX_RECONNECT_ATTEMPTS) {
      updateConnectionStatus('error');
      return;
    }

    reconnectAttemptsRef.current++;
    const delay = Math.min(RECONNECT_INTERVAL * reconnectAttemptsRef.current, 30000);

    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
    }

    reconnectTimerRef.current = setTimeout(() => {
      connect();
    }, delay);
  }, [updateConnectionStatus]);

  const connect = useCallback(() => {
    if (wsRef.current && (wsRef.current.readyState === WebSocket.CONNECTING || wsRef.current.readyState === WebSocket.OPEN)) {
      return;
    }

    updateConnectionStatus('connecting');

    try {
      // Add client ID to the WebSocket URL
      const clientId = username || `client_${Date.now()}`;
      const wsUrl = url.endsWith('/') ? `${url}${clientId}` : `${url}/${clientId}`;
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        updateConnectionStatus('connected');
        addMessage('system', 'Connected to support chat');
        reconnectAttemptsRef.current = 0;

        // Send initial join message
        if (wsRef.current?.readyState === WebSocket.OPEN) {
          const joinMessage: WebSocketMessage = {
            type: 'join',
            username,
            message: `${username} joined the chat`
          };
          wsRef.current.send(JSON.stringify(joinMessage));
        }
      };

      wsRef.current.onmessage = (event) => {
        let data: WebSocketMessage;
        try {
          data = JSON.parse(event.data);
        } catch (e) {
          data = { type: 'message', message: event.data, username: 'Support' };
        }

        // Handle RAG-specific message types
        if (data.type === 'rag_chunk') {
          // Handle streaming chunks
          if (currentStreamingMessageRef.current) {
            updateStreamingMessage(currentStreamingMessageRef.current, data.chunk || '');
          } else {
            // Start new streaming message
            const newMessage = addMessage('rag_response', data.chunk || '', 'Assistant', undefined, undefined, true, false);
            currentStreamingMessageRef.current = newMessage.id;
          }
        } else if (data.type === 'rag_complete') {
          setIsTyping(false);
          if (currentStreamingMessageRef.current) {
            // Complete the streaming message
            updateStreamingMessage(currentStreamingMessageRef.current, '', true, data.sources, data.metadata);
            currentStreamingMessageRef.current = null;
          } else {
            // Add complete message if no streaming was in progress
            addMessage('rag_response', data.message || '', 'Assistant', data.sources, data.metadata, false, true);
          }
        } else if (data.type === 'error') {
          setIsTyping(false);
          currentStreamingMessageRef.current = null;
          addMessage('system', data.message || 'An error occurred', 'System');
        } else if (data.type === 'system') {
          addMessage('system', data.message || '', 'System');
        } else if (data.username && data.username !== username) {
          addMessage('received', data.message || '', data.username);
        } else if (!data.username) {
          addMessage('received', data.message || event.data, 'Support');
        }
      };

      wsRef.current.onclose = (event) => {
        if (event.code === 1000) {
          updateConnectionStatus('disconnected');
        } else {
          updateConnectionStatus('error');
          scheduleReconnect();
        }
      };

      wsRef.current.onerror = () => {
        updateConnectionStatus('error');
      };

    } catch (error) {
      updateConnectionStatus('error');
      scheduleReconnect();
    }
  }, [url, username, updateConnectionStatus, addMessage, scheduleReconnect]);

  const disconnect = useCallback(() => {
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
    }
  }, []);

  const sendMessage = useCallback((message: string) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !message.trim()) {
      return false;
    }

    try {
      const messageData: WebSocketMessage = {
        type: 'message',
        username,
        message: message.trim(),
        timestamp: new Date().toISOString()
      };

      wsRef.current.send(JSON.stringify(messageData));
      addMessage('sent', message.trim());
      return true;
    } catch (error) {
      addMessage('system', 'Failed to send message');
      return false;
    }
  }, [username, addMessage]);

  const sendRAGQuery = useCallback((query: string) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN || !query.trim()) {
      return false;
    }

    try {
      const messageData: WebSocketMessage = {
        type: 'rag_query',
        username,
        message: query.trim(),
        timestamp: new Date().toISOString()
      };

      wsRef.current.send(JSON.stringify(messageData));
      addMessage('sent', query.trim());
      return true;
    } catch (error) {
      addMessage('system', 'Failed to send RAG query');
      return false;
    }
  }, [username, addMessage]);

  const retry = useCallback(() => {
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
    }
    reconnectAttemptsRef.current = 0;
    connect();
  }, [connect]);

  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  return {
    connectionStatus,
    isConnected,
    isTyping,
    messages,
    sendMessage,
    sendRAGQuery,
    retry,
    reconnectAttempts: reconnectAttemptsRef.current,
    maxAttempts: MAX_RECONNECT_ATTEMPTS
  };
};
