.source-citations {
  margin-top: 12px;
  border-top: 1px solid #e5e7eb;
  padding-top: 12px;
}

.sources-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.sources-icon {
  font-size: 16px;
}

.sources-title {
  color: var(--primary-color, #13183E);
}

.sources-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.source-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: #f9fafb;
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.source-header:hover {
  background: #f3f4f6;
}

.source-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.source-number {
  background: var(--primary-color, #13183E);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

.source-filename {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
  truncate: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  flex: 1;
}

.source-page {
  font-size: 12px;
  color: #6b7280;
  background: #e5e7eb;
  padding: 2px 6px;
  border-radius: 4px;
}

.source-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.source-score {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.8);
}

.expand-icon {
  font-size: 12px;
  color: #6b7280;
  transition: transform 0.2s;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.source-content {
  border-top: 1px solid #e5e7eb;
  padding: 12px;
  background: white;
}

.source-text {
  font-size: 13px;
  line-height: 1.5;
  color: #374151;
  margin-bottom: 12px;
  padding: 8px;
  background: #f9fafb;
  border-radius: 6px;
  border-left: 3px solid var(--primary-color, #13183E);
}

.source-metadata {
  border-top: 1px solid #f3f4f6;
  padding-top: 8px;
}

.metadata-title {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 6px;
}

.metadata-item {
  display: flex;
  gap: 6px;
  font-size: 11px;
  margin-bottom: 2px;
}

.metadata-key {
  color: #6b7280;
  font-weight: 500;
  min-width: 80px;
}

.metadata-value {
  color: #374151;
  word-break: break-all;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .source-header {
    padding: 8px;
  }
  
  .source-info {
    gap: 6px;
  }
  
  .source-filename {
    font-size: 12px;
  }
  
  .source-content {
    padding: 8px;
  }
  
  .source-text {
    font-size: 12px;
  }
}
