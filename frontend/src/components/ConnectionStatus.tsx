import React from 'react';
import { ConnectionStatus } from '../types';

interface ConnectionStatusProps {
  status: ConnectionStatus;
  onRetry: () => void;
  reconnectAttempts: number;
  maxAttempts: number;
}

export const ConnectionStatusComponent: React.FC<ConnectionStatusProps> = ({
  status,
  onRetry,
  reconnectAttempts,
  maxAttempts
}) => {
  const getStatusInfo = () => {
    switch (status) {
      case 'connecting':
        return {
          dotClass: 'connecting',
          text: 'Connecting...',
          showRetry: false
        };
      case 'connected':
        return {
          dotClass: 'connected',
          text: 'Connected',
          showRetry: false
        };
      case 'error':
      case 'disconnected':
        return {
          dotClass: 'error',
          text: reconnectAttempts >= maxAttempts ? 'Connection failed - Click retry' : 'Connection lost',
          showRetry: true
        };
      default:
        return {
          dotClass: 'error',
          text: 'Unknown status',
          showRetry: true
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div className="connection-status">
      <div className="status-indicator">
        <div className={`status-dot ${statusInfo.dotClass}`}></div>
        <span className="status-text">{statusInfo.text}</span>
      </div>
      {statusInfo.showRetry && (
        <button className="retry-btn" onClick={onRetry}>
          Retry
        </button>
      )}
    </div>
  );
};
