.chat-widget-container {
  --primary-color: #13183E;
  --secondary-color: #0F1333;
  --button-color: #D7492A;
  --accent-orange: #D7492A;
  --background-color: #FFFFFF;
  --text-primary: #FFFFFF;
  --text-secondary: #666666;
  --user-bubble: #E0F2F1;
  --bot-bubble: #F8F9FA;
  --bot-bubble-alt: #FFFFFF;
  position: fixed;
  z-index: 1000;
  font-family: 'Montserrat', sans-serif;
}

/* Floating Chat <PERSON><PERSON> */
.floating-chat-button {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--button-color), #B8402A);
  border-radius: 50%;
  box-shadow: 0 8px 25px rgba(0,0,0,0.3);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  transition: all 0.3s ease;
  border: none;
  position: fixed;
}

.floating-chat-button:hover {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(0,0,0,0.4);
}

.floating-chat-button.active {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  animation: buttonPulse 0.3s ease;
}

@keyframes buttonPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Button positions */
.button-bottom-right {
  bottom: 30px;
  right: 30px;
}

.button-bottom-left {
  bottom: 30px;
  left: 30px;
}

.button-top-right {
  top: 30px;
  right: 30px;
}

.button-top-left {
  top: 30px;
  left: 30px;
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: var(--button-color);
  color: var(--text-primary);
  font-size: 12px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
  animation: bounce 1s infinite, badgeAppear 0.3s ease;
}

@keyframes badgeAppear {
  from {
    opacity: 0;
    transform: scale(0) rotate(180deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

/* Chat Widget */
.chat-widget {
  width: 380px;
  height: 550px;
  background: var(--background-color);
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0,0,0,0.4);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideInUp 0.4s ease;
  position: fixed;
  border: 1px solid #E0E0E0;
}

.chat-widget.closing {
  animation: slideOutDown 0.3s ease forwards;
}

/* Widget positions */
.position-bottom-right {
  bottom: 100px;
  right: 30px;
}

.position-bottom-left {
  bottom: 100px;
  left: 30px;
}

.position-top-right {
  top: 100px;
  right: 30px;
}

.position-top-left {
  top: 100px;
  left: 30px;
}

/* Position-specific closing animations */
.position-bottom-right.closing,
.position-bottom-left.closing {
  animation: slideOutDown 0.3s ease forwards;
}

.position-top-right.closing,
.position-top-left.closing {
  animation: slideOutUp 0.3s ease forwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideOutDown {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
}

@keyframes slideOutUp {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
}

/* Chat Header */
.chat-header {
  background: var(--primary-color);
  color: var(--text-primary);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  border-bottom: 2px solid var(--button-color);
}

.chat-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.05), transparent);
}

.chat-title {
  font-size: 18px;
  font-weight: 600;
  z-index: 1;
  color: var(--text-primary);
}

.close-btn {
  background: rgba(255,255,255,0.2);
  border: none;
  color: var(--text-primary);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.3s ease;
  z-index: 1;
}

.close-btn:hover {
  background: rgba(255,255,255,0.3);
  transform: rotate(90deg);
}

.close-btn:active {
  background: rgba(255,255,255,0.4);
  transform: rotate(180deg) scale(0.9);
}

/* Connection Status */
.connection-status {
  background: #D7492A;
  padding: 12px 20px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 13px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff4757;
  animation: pulse 2s infinite;
}

.status-dot.connecting {
  background: #ffa502;
}

.status-dot.connected {
  background: #2ed573;
  animation: none;
}

.status-dot.error {
  background: var(--button-color);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-weight: 500;
  color: var(--text-primary);
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.retry-btn {
  background: var(--primary-color);
  color: var(--text-primary);
  border: none;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 11px;
  cursor: pointer;
  margin-left: auto;
  transition: all 0.2s ease;
  font-weight: 600;
}

.retry-btn:hover {
  background: var(--secondary-color);
  transform: translateY(-1px);
}

/* Messages Container */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: var(--background-color);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message {
  max-width: 80%;
  padding: 10px 15px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  animation: messageSlide 0.3s ease;
  position: relative;
}

@keyframes messageSlide {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.sent {
  align-self: flex-end;
  background: var(--user-bubble);
  color: #2e7d32;
  border-bottom-right-radius: 5px;
}

.message.received {
  align-self: flex-start;
  background: var(--bot-bubble);
  color: #333;
  border: 1px solid #E0E0E0;
  border-bottom-left-radius: 5px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.message.system {
  align-self: center;
  background: rgba(19, 24, 62, 0.1);
  color: var(--text-secondary);
  font-style: italic;
  font-size: 12px;
  border-radius: 12px;
  max-width: 90%;
  text-align: center;
}

.message-time {
  font-size: 10px;
  margin-top: 4px;
  opacity: 0.7;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
}

.empty-state-icon {
  font-size: 40px;
  margin-bottom: 12px;
  opacity: 0.5;
}

/* Input Container */
.input-container {
  padding: 15px 20px;
  background: #D7492A;
  border-top: 1px solid rgba(255,255,255,0.1);
  display: flex;
  gap: 10px;
  align-items: center;
}

.message-input {
  flex: 1;
  padding: 12px 18px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 25px;
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
  resize: none;
  max-height: 100px;
  background: rgba(255,255,255,0.95);
  color: #333;
  font-weight: 500;
}

.message-input:focus {
  border-color: #13183E;
  box-shadow: 0 0 0 3px rgba(19, 24, 62, 0.2);
  background: white;
}

.message-input::placeholder {
  color: #666;
  font-weight: 400;
}

.send-btn {
  background: var(--button-color);
  color: var(--text-primary);
  border: none;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(215, 73, 42, 0.3);
}

.send-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(215, 73, 42, 0.4);
  background: #B8402A;
}

.send-btn:disabled {
  background: #666;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Scrollbar */
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .chat-widget {
    width: calc(100vw - 20px);
    height: calc(100vh - 120px);
  }
  
  .position-bottom-right,
  .position-bottom-left {
    right: 10px;
    left: 10px;
    bottom: 90px;
  }
  
  .position-top-right,
  .position-top-left {
    right: 10px;
    left: 10px;
    top: 90px;
  }
  
  .button-bottom-right,
  .button-bottom-left {
    right: 20px;
    left: auto;
    bottom: 20px;
  }
  
  .button-top-right,
  .button-top-left {
    right: 20px;
    left: auto;
    top: 20px;
  }
}

/* RAG-specific message styles */
.message.rag_response {
  align-self: flex-start;
  background: var(--bot-bubble);
  color: #333;
  border: 1px solid #E0E0E0;
  border-bottom-left-radius: 5px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  max-width: 90%;
}

.message.typing {
  align-self: flex-start;
  background: var(--bot-bubble);
  color: #666;
  border: 1px solid #E0E0E0;
  border-bottom-left-radius: 5px;
  max-width: 70%;
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rag-response {
  margin-top: 4px;
  line-height: 1.5;
}

.typing-cursor {
  animation: blink 1s infinite;
  font-weight: bold;
  color: var(--primary-color, #13183E);
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.typing-dots {
  display: flex;
  gap: 2px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  background: #666;
  border-radius: 50%;
  animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
