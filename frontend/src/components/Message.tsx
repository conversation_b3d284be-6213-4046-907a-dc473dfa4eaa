import React from 'react';
import { ChatMessage } from '../types';
import { SourceCitation } from './SourceCitation';

interface MessageProps {
  message: ChatMessage;
}

export const Message: React.FC<MessageProps> = ({ message }) => {
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderContent = () => {
    const time = formatTime(message.timestamp);

    if (message.type === 'rag_response') {
      return (
        <>
          <div className="message-content">
            <strong>Assistant:</strong>
            <div className="rag-response">
              {message.message}
              {message.isStreaming && <span className="typing-cursor">|</span>}
            </div>
            {message.sources && message.sources.length > 0 && (
              <SourceCitation sources={message.sources} />
            )}
          </div>
          <div className="message-time">{time}</div>
        </>
      );
    } else if (message.type === 'typing') {
      return (
        <>
          <strong>Assistant:</strong>
          <span className="typing-indicator">
            {message.message}
            <span className="typing-dots">
              <span>.</span>
              <span>.</span>
              <span>.</span>
            </span>
          </span>
        </>
      );
    } else if (message.type === 'received' && message.sender) {
      return (
        <>
          <strong>{message.sender}:</strong> {message.message}
          <div className="message-time">{time}</div>
        </>
      );
    } else if (message.type === 'sent') {
      return (
        <>
          {message.message}
          <div className="message-time">{time}</div>
        </>
      );
    } else {
      return message.message;
    }
  };

  return (
    <div className={`message ${message.type}`}>
      {renderContent()}
    </div>
  );
};
