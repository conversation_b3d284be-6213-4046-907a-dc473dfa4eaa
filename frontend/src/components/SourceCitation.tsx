import React, { useState } from 'react';
import { SourceInfo } from '../types';
import './SourceCitation.css';

interface SourceCitationProps {
  sources: SourceInfo[];
}

export const SourceCitation: React.FC<SourceCitationProps> = ({ sources }) => {
  const [expandedSources, setExpandedSources] = useState<Set<number>>(new Set());

  if (!sources || sources.length === 0) {
    return null;
  }

  const toggleSource = (chunkId: number) => {
    const newExpanded = new Set(expandedSources);
    if (newExpanded.has(chunkId)) {
      newExpanded.delete(chunkId);
    } else {
      newExpanded.add(chunkId);
    }
    setExpandedSources(newExpanded);
  };

  const getFileName = (filePath?: string) => {
    if (!filePath) return 'Unknown Document';
    const parts = filePath.split('/');
    return parts[parts.length - 1] || 'Unknown Document';
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return '#22c55e'; // green
    if (score >= 0.6) return '#eab308'; // yellow
    return '#ef4444'; // red
  };

  return (
    <div className="source-citations">
      <div className="sources-header">
        <span className="sources-icon">📚</span>
        <span className="sources-title">Sources ({sources.length})</span>
      </div>
      
      <div className="sources-list">
        {sources.map((source) => (
          <div key={source.chunk_id} className="source-item">
            <div 
              className="source-header"
              onClick={() => toggleSource(source.chunk_id)}
            >
              <div className="source-info">
                <span className="source-number">#{source.chunk_id}</span>
                <span className="source-filename">
                  {getFileName(source.metadata.file_path)}
                </span>
                {source.metadata.page_label && (
                  <span className="source-page">
                    Page {source.metadata.page_label}
                  </span>
                )}
              </div>
              
              <div className="source-meta">
                <span 
                  className="source-score"
                  style={{ color: getScoreColor(source.score) }}
                >
                  {(source.score * 100).toFixed(1)}%
                </span>
                <span className={`expand-icon ${expandedSources.has(source.chunk_id) ? 'expanded' : ''}`}>
                  ▼
                </span>
              </div>
            </div>
            
            {expandedSources.has(source.chunk_id) && (
              <div className="source-content">
                <div className="source-text">
                  {source.text}
                </div>
                
                {Object.keys(source.metadata).length > 0 && (
                  <div className="source-metadata">
                    <div className="metadata-title">Metadata:</div>
                    {Object.entries(source.metadata).map(([key, value]) => (
                      <div key={key} className="metadata-item">
                        <span className="metadata-key">{key}:</span>
                        <span className="metadata-value">{String(value)}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
