import React, { useState, useRef, useEffect } from 'react';
import { useWebSocket } from '../hooks/useWebSocket';
import { ConnectionStatusComponent } from './ConnectionStatus';
import { Message } from './Message';
import { ChatWidgetProps } from '../types';
import { appConfig } from '../config/environment';
import './ChatWidget.css';

const defaultProps: Required<ChatWidgetProps> = {
  wsUrl: appConfig.websocketUrl,
  username: appConfig.defaultUsername,
  title: appConfig.appTitle,
  position: 'bottom-right',
  theme: appConfig.theme,
  onConnectionChange: () => {},
  onMessageReceived: () => {}
};

export const ChatWidget: React.FC<ChatWidgetProps> = (props) => {
  const config = { ...defaultProps, ...props };
  const [isOpen, setIsOpen] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [unreadMessages, setUnreadMessages] = useState(0);
  const [inputValue, setInputValue] = useState('');
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const {
    connectionStatus,
    isConnected,
    isTyping,
    messages,
    sendMessage,
    sendRAGQuery,
    retry,
    reconnectAttempts,
    maxAttempts
  } = useWebSocket({
    url: config.wsUrl,
    username: config.username,
    onConnectionChange: config.onConnectionChange
  });

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
    }
  }, [messages]);

  // Track unread messages
  useEffect(() => {
    if (!isOpen && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.type === 'received') {
        setUnreadMessages(prev => prev + 1);
        config.onMessageReceived(lastMessage);
      }
    }
  }, [messages, isOpen, config]);

  const handleToggleChat = () => {
    if (isOpen) {
      handleCloseChat();
    } else {
      setIsOpen(true);
      setIsClosing(false);
      setUnreadMessages(0);
    }
  };

  const handleCloseChat = () => {
    setIsClosing(true);
    // Wait for animation to complete before hiding
    setTimeout(() => {
      setIsOpen(false);
      setIsClosing(false);
    }, 300); // Match the animation duration
  };

  const handleSendMessage = () => {
    if (inputValue.trim() && sendRAGQuery(inputValue)) {
      setInputValue('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getPositionClasses = () => {
    const baseClasses = 'chat-widget';
    switch (config.position) {
      case 'bottom-left':
        return `${baseClasses} position-bottom-left`;
      case 'top-right':
        return `${baseClasses} position-top-right`;
      case 'top-left':
        return `${baseClasses} position-top-left`;
      default:
        return `${baseClasses} position-bottom-right`;
    }
  };

  const getButtonPositionClasses = () => {
    const baseClasses = 'floating-chat-button';
    switch (config.position) {
      case 'bottom-left':
        return `${baseClasses} button-bottom-left`;
      case 'top-right':
        return `${baseClasses} button-top-right`;
      case 'top-left':
        return `${baseClasses} button-top-left`;
      default:
        return `${baseClasses} button-bottom-right`;
    }
  };

  const customStyles = {
    '--primary-color': config.theme.primaryColor,
    '--secondary-color': config.theme.secondaryColor,
    '--button-color': config.theme.buttonColor
  } as React.CSSProperties;

  return (
    <div className="chat-widget-container" style={customStyles}>
      {/* Floating Chat Button */}
      <button
        className={`${getButtonPositionClasses()} ${isOpen ? 'active' : ''}`}
        onClick={handleToggleChat}
      >
        💬
        {unreadMessages > 0 && (
          <div className="notification-badge">
            {unreadMessages > 99 ? '99+' : unreadMessages}
          </div>
        )}
      </button>

      {/* Chat Widget */}
      {isOpen && (
        <div className={`${getPositionClasses()} ${isClosing ? 'closing' : ''}`}>
          <div className="chat-header">
            <div className="chat-title">{config.title}</div>
            <button className="close-btn" onClick={handleCloseChat}>
              &times;
            </button>
          </div>

          <ConnectionStatusComponent
            status={connectionStatus}
            onRetry={retry}
            reconnectAttempts={reconnectAttempts}
            maxAttempts={maxAttempts}
          />

          <div className="messages-container scrollbar-thin" ref={messagesContainerRef}>
            {messages.length === 0 ? (
              <div className="empty-state">
                <div className="empty-state-icon">💬</div>
                <p>Start a conversation!</p>
              </div>
            ) : (
              messages.map((message) => (
                <Message key={message.id} message={message} />
              ))
            )}
          </div>

          <div className="input-container">
            <input
              type="text"
              className="message-input"
              placeholder={isConnected ? "Type your message..." : "Reconnecting..."}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={!isConnected}
            />
            <button
              className="send-btn"
              onClick={handleSendMessage}
              disabled={!isConnected || !inputValue.trim()}
            >
              ➤
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
