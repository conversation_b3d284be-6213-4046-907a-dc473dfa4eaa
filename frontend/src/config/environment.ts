// Environment configuration for the frontend application

export interface AppConfig {
  websocketUrl: string;
  appTitle: string;
  defaultUsername: string;
  theme: {
    primaryColor: string;
    secondaryColor: string;
    buttonColor: string;
  };
  devMode: boolean;
}

// Get environment variable with fallback
const getEnvVar = (key: string, fallback: string): string => {
  return import.meta.env[key] || fallback;
};

// Get boolean environment variable
const getBoolEnvVar = (key: string, fallback: boolean): boolean => {
  const value = import.meta.env[key];
  if (value === undefined) return fallback;
  return value.toLowerCase() === 'true';
};

// Application configuration
export const appConfig: AppConfig = {
  websocketUrl: getEnvVar('VITE_WEBSOCKET_URL', 'ws://localhost:8000/ws'),
  appTitle: getEnvVar('VITE_APP_TITLE', 'UM6P Policies Assistant'),
  defaultUsername: getEnvVar('VITE_DEFAULT_USERNAME', `User_${Math.random().toString(36).substr(2, 5)}`),
  theme: {
    primaryColor: getEnvVar('VITE_PRIMARY_COLOR', '#13183E'),
    secondaryColor: getEnvVar('VITE_SECONDARY_COLOR', '#0F1333'),
    buttonColor: getEnvVar('VITE_BUTTON_COLOR', '#D7492A'),
  },
  devMode: getBoolEnvVar('VITE_DEV_MODE', true),
};

// Export individual config values for convenience
export const {
  websocketUrl,
  appTitle,
  defaultUsername,
  theme,
  devMode,
} = appConfig;

// Development helpers
export const isDevelopment = devMode;
export const isProduction = !devMode;

// Logging helper that respects dev mode
export const devLog = (...args: any[]) => {
  if (isDevelopment) {
    console.log('[DEV]', ...args);
  }
};

export const devWarn = (...args: any[]) => {
  if (isDevelopment) {
    console.warn('[DEV]', ...args);
  }
};

export const devError = (...args: any[]) => {
  if (isDevelopment) {
    console.error('[DEV]', ...args);
  }
};
