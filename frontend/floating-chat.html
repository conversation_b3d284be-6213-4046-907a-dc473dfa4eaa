<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Floating WebSocket Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .page-content {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            color: white;
        }

        .floating-chat-button {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 50%;
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            transition: all 0.3s ease;
            z-index: 1001;
            border: none;
        }

        .floating-chat-button:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(0,0,0,0.4);
        }

        .floating-chat-button.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            font-size: 12px;
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 20px;
            text-align: center;
            display: none;
            animation: bounce 1s infinite;
        }

        @keyframes bounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

        .chat-widget {
            position: fixed;
            bottom: 100px;
            right: 30px;
            width: 380px;
            height: 550px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.4);
            display: none;
            flex-direction: column;
            overflow: hidden;
            z-index: 1000;
            animation: slideInUp 0.4s ease;
        }

        .chat-widget.active {
            display: flex;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .chat-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), transparent);
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
            z-index: 1;
        }

        .close-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s ease;
            z-index: 1;
        }

        .close-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: rotate(90deg);
        }

        .connection-status {
            background: #f8f9fa;
            padding: 12px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 13px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ff4757;
            animation: pulse 2s infinite;
        }

        .status-dot.connecting {
            background: #ffa502;
        }

        .status-dot.connected {
            background: #2ed573;
            animation: none;
        }

        .status-dot.error {
            background: #ff4757;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .status-text {
            font-weight: 500;
            color: #666;
        }

        .retry-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            cursor: pointer;
            margin-left: auto;
            transition: all 0.2s ease;
        }

        .retry-btn:hover {
            background: #5a67d8;
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .message {
            max-width: 80%;
            padding: 10px 15px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
            animation: messageSlide 0.3s ease;
            position: relative;
        }

        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.sent {
            align-self: flex-end;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.received {
            align-self: flex-start;
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .message.system {
            align-self: center;
            background: #e3f2fd;
            color: #1565c0;
            font-style: italic;
            font-size: 12px;
            border-radius: 12px;
            max-width: 90%;
            text-align: center;
        }

        .message-time {
            font-size: 10px;
            margin-top: 4px;
            opacity: 0.7;
        }

        .input-container {
            padding: 15px 20px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 20px;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
            resize: none;
            max-height: 100px;
        }

        .message-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .send-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .send-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            text-align: center;
        }

        .empty-state-icon {
            font-size: 40px;
            margin-bottom: 12px;
            opacity: 0.5;
        }

        .scrollbar-thin::-webkit-scrollbar {
            width: 4px;
        }

        .scrollbar-thin::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        @media (max-width: 480px) {
            .chat-widget {
                width: calc(100vw - 20px);
                height: calc(100vh - 120px);
                right: 10px;
                bottom: 90px;
            }
            
            .floating-chat-button {
                right: 20px;
                bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Demo page content -->
    <div class="page-content">
        <h1>WebSocket Chat Widget Demo</h1>
        <p>This page demonstrates a floating chat widget that automatically connects to a WebSocket server.</p>
        <p>Click the chat button in the bottom right to start chatting!</p>
        <br>
        <p>The widget will automatically attempt to connect to: <strong>ws://localhost:8080</strong></p>
        <p>You can modify the WebSocket URL in the JavaScript code.</p>
    </div>

    <!-- Floating Chat Button -->
    <button class="floating-chat-button" id="chatButton" onclick="toggleChat()">
        💬
        <div class="notification-badge" id="notificationBadge">0</div>
    </button>

    <!-- Chat Widget -->
    <div class="chat-widget" id="chatWidget">
        <div class="chat-header">
            <div class="chat-title">Support Chat</div>
            <button class="close-btn" onclick="closeChat()">&times;</button>
        </div>

        <div class="connection-status">
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span class="status-text" id="statusText">Connecting...</span>
            </div>
            <button class="retry-btn" id="retryBtn" onclick="reconnectWebSocket()" style="display: none;">
                Retry
            </button>
        </div>

        <div class="messages-container scrollbar-thin" id="messagesContainer">
            <div class="empty-state" id="emptyState">
                <div class="empty-state-icon">💬</div>
                <p>Start a conversation!</p>
            </div>
        </div>

        <div class="input-container">
            <input 
                type="text" 
                class="message-input" 
                id="messageInput" 
                placeholder="Type your message..."
                onkeypress="handleKeyPress(event)"
            >
            <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                ➤
            </button>
        </div>
    </div>

    <script>
        // Configuration
        const WS_URL = 'ws://localhost:8080'; // Change this to your WebSocket server URL
        const USERNAME = 'User_' + Math.random().toString(36).substr(2, 5); // Generate random username
        const RECONNECT_INTERVAL = 5000; // 5 seconds
        const MAX_RECONNECT_ATTEMPTS = 10;

        // State
        let ws = null;
        let isConnected = false;
        let isChatOpen = false;
        let reconnectAttempts = 0;
        let reconnectTimer = null;
        let unreadMessages = 0;

        // Initialize chat when page loads
        document.addEventListener('DOMContentLoaded', function() {
            connectWebSocket();
        });

        function toggleChat() {
            if (isChatOpen) {
                closeChat();
            } else {
                openChat();
            }
        }

        function openChat() {
            const chatWidget = document.getElementById('chatWidget');
            const chatButton = document.getElementById('chatButton');
            
            chatWidget.classList.add('active');
            chatButton.classList.add('active');
            isChatOpen = true;
            
            // Clear unread messages
            unreadMessages = 0;
            updateNotificationBadge();
            
            // Focus on input
            document.getElementById('messageInput').focus();
        }

        function closeChat() {
            const chatWidget = document.getElementById('chatWidget');
            const chatButton = document.getElementById('chatButton');
            
            chatWidget.classList.remove('active');
            chatButton.classList.remove('active');
            isChatOpen = false;
        }

        function updateConnectionStatus(status, message) {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            const retryBtn = document.getElementById('retryBtn');
            const messageInput = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            
            // Remove all status classes
            statusDot.className = 'status-dot';
            
            switch(status) {
                case 'connecting':
                    statusDot.classList.add('connecting');
                    statusText.textContent = message || 'Connecting...';
                    retryBtn.style.display = 'none';
                    isConnected = false;
                    break;
                    
                case 'connected':
                    statusDot.classList.add('connected');
                    statusText.textContent = message || 'Connected';
                    retryBtn.style.display = 'none';
                    isConnected = true;
                    reconnectAttempts = 0;
                    break;
                    
                case 'error':
                case 'disconnected':
                    statusDot.classList.add('error');
                    statusText.textContent = message || 'Connection lost';
                    retryBtn.style.display = 'block';
                    isConnected = false;
                    break;
            }
            
            // Enable/disable input controls
            messageInput.disabled = !isConnected;
            sendBtn.disabled = !isConnected;
            
            if (!isConnected) {
                messageInput.placeholder = 'Reconnecting...';
            } else {
                messageInput.placeholder = 'Type your message...';
            }
        }

        function connectWebSocket() {
            if (ws && (ws.readyState === WebSocket.CONNECTING || ws.readyState === WebSocket.OPEN)) {
                return;
            }
            
            updateConnectionStatus('connecting');
            
            try {
                ws = new WebSocket(WS_URL);
                
                ws.onopen = function(event) {
                    updateConnectionStatus('connected');
                    addMessage('system', 'Connected to support chat');
                    
                    // Hide empty state
                    document.getElementById('emptyState').style.display = 'none';
                    
                    // Send initial join message
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'join',
                            username: USERNAME,
                            message: `${USERNAME} joined the chat`
                        }));
                    }
                };
                
                ws.onmessage = function(event) {
                    let data;
                    try {
                        data = JSON.parse(event.data);
                    } catch (e) {
                        data = { message: event.data, username: 'Support' };
                    }
                    
                    if (data.username && data.username !== USERNAME) {
                        addMessage('received', data.message, data.username);
                        
                        // Show notification if chat is closed
                        if (!isChatOpen) {
                            unreadMessages++;
                            updateNotificationBadge();
                        }
                    } else if (data.type === 'system') {
                        addMessage('system', data.message);
                    } else if (!data.username) {
                        addMessage('received', data.message || event.data, 'Support');
                        
                        if (!isChatOpen) {
                            unreadMessages++;
                            updateNotificationBadge();
                        }
                    }
                };
                
                ws.onclose = function(event) {
                    if (event.code === 1000) {
                        updateConnectionStatus('disconnected', 'Disconnected');
                    } else {
                        updateConnectionStatus('error', 'Connection lost');
                        scheduleReconnect();
                    }
                };
                
                ws.onerror = function(error) {
                    updateConnectionStatus('error', 'Connection error');
                    console.error('WebSocket error:', error);
                };
                
            } catch (error) {
                updateConnectionStatus('error', 'Failed to connect');
                console.error('Connection error:', error);
                scheduleReconnect();
            }
        }

        function scheduleReconnect() {
            if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
                updateConnectionStatus('error', 'Connection failed - Click retry');
                return;
            }
            
            reconnectAttempts++;
            const delay = Math.min(RECONNECT_INTERVAL * reconnectAttempts, 30000); // Max 30 seconds
            
            updateConnectionStatus('error', `Reconnecting in ${Math.ceil(delay/1000)}s... (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);
            
            if (reconnectTimer) {
                clearTimeout(reconnectTimer);
            }
            
            reconnectTimer = setTimeout(() => {
                connectWebSocket();
            }, delay);
        }

        function reconnectWebSocket() {
            if (reconnectTimer) {
                clearTimeout(reconnectTimer);
            }
            reconnectAttempts = 0;
            connectWebSocket();
        }

        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message || !isConnected || !ws) return;
            
            try {
                ws.send(JSON.stringify({
                    type: 'message',
                    username: USERNAME,
                    message: message,
                    timestamp: new Date().toISOString()
                }));
                
                addMessage('sent', message);
                messageInput.value = '';
                
            } catch (error) {
                addMessage('system', 'Failed to send message');
                console.error('Send error:', error);
            }
        }

        function addMessage(type, message, sender = null) {
            const container = document.getElementById('messagesContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            
            let content = '';
            if (type === 'received' && sender) {
                content = `<strong>${sender}:</strong> ${message}<div class="message-time">${time}</div>`;
            } else if (type === 'sent') {
                content = `${message}<div class="message-time">${time}</div>`;
            } else {
                content = message;
            }
            
            messageDiv.innerHTML = content;
            container.appendChild(messageDiv);
            
            // Auto scroll to bottom
            container.scrollTop = container.scrollHeight;
        }

        function updateNotificationBadge() {
            const badge = document.getElementById('notificationBadge');
            if (unreadMessages > 0) {
                badge.textContent = unreadMessages > 99 ? '99+' : unreadMessages;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // Close chat when clicking outside
        document.addEventListener('click', function(event) {
            const chatWidget = document.getElementById('chatWidget');
            const chatButton = document.getElementById('chatButton');
            
            if (isChatOpen && !chatWidget.contains(event.target) && !chatButton.contains(event.target)) {
                closeChat();
            }
        });

        // Handle page visibility change to manage connection
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // Page is hidden, you might want to reduce connection attempts
            } else {
                // Page is visible, ensure connection is active
                if (!isConnected && ws && ws.readyState === WebSocket.CLOSED) {
                    connectWebSocket();
                }
            }
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (ws) {
                ws.close(1000, 'Page unloading');
            }
            if (reconnectTimer) {
                clearTimeout(reconnectTimer);
            }
        });
    </script>
</body>
</html>
