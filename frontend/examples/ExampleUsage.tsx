import React from 'react';
import { ChatWidget } from '../src/components/ChatWidget';

// Example component showing how to use the ChatWidget
const ExampleUsage: React.FC = () => {
  return (
    <div>
      {/* Your existing app content */}
      <div style={{ padding: '20px' }}>
        <h1>My Application</h1>
        <p>This is my existing application content...</p>
      </div>

      {/* Simply add the ChatWidget component anywhere in your app */}
      <ChatWidget
        wsUrl="ws://localhost:8080"
        username="Customer123"
        title="Customer Support"
        position="bottom-right"
        theme={{
          primaryColor: "#667eea",
          secondaryColor: "#764ba2",
          buttonColor: "#ff6b6b"
        }}
        onConnectionChange={(status) => {
          console.log('Chat connection status:', status);
        }}
        onMessageReceived={(message) => {
          console.log('New message received:', message);
          // You can handle notifications, analytics, etc. here
        }}
      />
    </div>
  );
};

export default ExampleUsage;
