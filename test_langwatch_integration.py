#!/usr/bin/env python3
"""
Test script for LangWatch integration with AdvancedRAGPipeline

This script tests the LangWatch integration without requiring a real API key
by using mock/test mode.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.ragPipeline import AdvancedRAGPipeline
from core.langwatch_config import get_langwatch_config

def test_langwatch_config():
    """Test LangWatch configuration without API key"""
    print("🧪 Testing LangWatch Configuration...")
    
    # Test with disabled LangWatch (no API key)
    config = get_langwatch_config()
    status = config.get_status()
    
    print(f"  ✓ LangWatch available: {status['available']}")
    print(f"  ✓ LangWatch enabled: {status['enabled']}")
    print(f"  ✓ API key configured: {status['api_key_configured']}")
    print(f"  ✓ Project name: {status['project_name']}")
    
    return status['available']

def test_pipeline_initialization():
    """Test RAG pipeline initialization with Lang<PERSON>atch"""
    print("\n🧪 Testing Pipeline Initialization...")
    
    try:
        # Initialize pipeline with LangWatch enabled but no API key
        rag = AdvancedRAGPipeline(
            persist_dir="./storage",
            enable_langwatch=True,
            langwatch_project_name="test-rag-pipeline",
            langwatch_version="test-1.0.0"
        )
        
        print("  ✓ Pipeline initialized successfully")
        
        # Check LangWatch status
        langwatch_status = rag.get_langwatch_status()
        print(f"  ✓ LangWatch status: {langwatch_status}")
        
        # Check pipeline config includes LangWatch info
        config = rag.get_pipeline_config()
        print(f"  ✓ LangWatch in config: {'langwatch' in config}")
        
        return rag
        
    except Exception as e:
        print(f"  ❌ Pipeline initialization failed: {e}")
        return None

def test_chat_functionality(rag):
    """Test chat functionality with LangWatch tracking"""
    print("\n🧪 Testing Chat Functionality...")
    
    if rag is None:
        print("  ⚠️ Skipping chat test - pipeline not initialized")
        return
    
    try:
        # Try to load existing index
        if not rag.load_existing_index():
            print("  ⚠️ No existing index found - skipping chat test")
            print("  💡 To test chat functionality, first create an index with PDF documents")
            return
        
        # Setup chat engine
        rag.setup_chat_engine()
        print("  ✓ Chat engine setup successful")
        
        # Test a simple chat (this will work even without LangWatch API key)
        test_message = "What is the university's policy on research integrity?"
        print(f"  🔍 Testing query: '{test_message}'")
        
        # Test regular chat
        response = rag.chat(
            message=test_message,
            session_id="test_session_123"
        )
        
        print(f"  ✓ Chat response received (length: {len(response['response'])} chars)")
        print(f"  ✓ Sources found: {response['metadata']['num_sources']}")
        print(f"  ✓ LangWatch trace ID: {response.get('langwatch_trace_id', 'None')}")
        print(f"  ✓ Latency: {response['metadata'].get('latency_ms', 'N/A')} ms")
        
        # Test session context setting
        rag.set_langwatch_session_context(
            session_id="test_session_123",
            user_id="test_user",
            additional_context={"test_mode": True}
        )
        print("  ✓ Session context set successfully")
        
    except Exception as e:
        print(f"  ❌ Chat test failed: {e}")
        import traceback
        traceback.print_exc()

def test_websocket_server_integration():
    """Test that websocket server can still initialize with LangWatch"""
    print("\n🧪 Testing WebSocket Server Integration...")
    
    try:
        # Import websocket server to check for import errors
        import websocket_server
        print("  ✓ WebSocket server imports successfully")
        
        # Check if RAGWebSocketServer can be initialized
        server = websocket_server.RAGWebSocketServer()
        print("  ✓ RAGWebSocketServer initialized successfully")
        
        if server.rag_pipeline:
            langwatch_status = server.rag_pipeline.get_langwatch_status()
            print(f"  ✓ WebSocket RAG pipeline LangWatch status: {langwatch_status['enabled']}")
        
    except Exception as e:
        print(f"  ❌ WebSocket server test failed: {e}")

def main():
    """Run all tests"""
    print("🚀 Testing LangWatch Integration with AdvancedRAGPipeline")
    print("=" * 60)
    
    # Test 1: LangWatch configuration
    langwatch_available = test_langwatch_config()
    
    # Test 2: Pipeline initialization
    rag = test_pipeline_initialization()
    
    # Test 3: Chat functionality (if index exists)
    test_chat_functionality(rag)
    
    # Test 4: WebSocket server integration
    test_websocket_server_integration()
    
    print("\n" + "=" * 60)
    print("🎉 LangWatch Integration Tests Complete!")
    
    if langwatch_available:
        print("\n💡 To enable full LangWatch tracking:")
        print("   1. Get an API key from https://langwatch.ai")
        print("   2. Set LANGWATCH_API_KEY in your .env file")
        print("   3. Set LANGWATCH_ENABLED=true in your .env file")
    else:
        print("\n⚠️ LangWatch is not installed or not available")
        print("   Install with: pip install langwatch")

if __name__ == "__main__":
    main()
