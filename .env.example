# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# RAG Pipeline Configuration
RAG_PERSIST_DIR=./storage
RAG_COLLECTION_NAME=rag_collection
USE_RERANKER=true
RERANKER_MODEL=BAAI/bge-reranker-base
RERANKER_DEVICE=cpu
LLM_MODEL=gpt-4o
EMBEDDING_MODEL=text-embedding-3-small
CHUNK_SIZE=500
CHUNK_OVERLAP=50
RERANKER_TOP_N=5

# WebSocket Server Configuration
WEBSOCKET_HOST=0.0.0.0
WEBSOCKET_PORT=8000
LOG_LEVEL=info
ALLOWED_ORIGINS=*

# LangWatch Configuration
LANGWATCH_ENABLED=true
LANGWATCH_API_KEY=your_langwatch_api_key_here
LANGWATCH_PROJECT_NAME=rag-policy-pipeline
LANGWATCH_VERSION=1.0.0
LANGWATCH_USER_ID=
