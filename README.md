# Policy RAG Chat System - UM6P

An advanced Retrieval-Augmented Generation (RAG) system designed specifically for querying UM6P (Mohammed VI Polytechnic University) policy documents with conversational AI capabilities.

## 🎯 Overview

This system allows users to interact with university policy documents through a conversational AI interface. Built with LlamaIndex and powered by OpenAI's GPT models, it provides accurate, cited responses based on official UM6P policy documents.

## ✨ Features

- 🤖 **Conversational AI**: Ask follow-up questions naturally with context awareness
- 📚 **PDF Processing**: Automatically processes and indexes policy documents
- 🔍 **Advanced Retrieval**: Reranking for improved search accuracy
- 🎯 **Source Citations**: Every response includes proper source citations
- 💾 **Persistent Storage**: ChromaDB vector storage with automatic persistence
- ⚙️ **Configurable**: Customizable chunking, retrieval, and response settings
- 🏫 **UM6P Specific**: Tailored system prompts for university policy assistance
- 🔒 **Accurate Responses**: Only responds based on provided documents

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- OpenAI API key
- PDF policy documents

### Installation

1. **Install dependencies:**
   ```bash
   uv pip install -r requirements.txt
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env and add your OpenAI API key
   ```

3. **Run the system:**
   ```bash
   python main.py
   ```

## 📁 Project Structure

```
policiesRAG/
├── main.py                    # Main entry point
├── core/
│   └── ragPipeline.py        # Core RAG pipeline logic
├── data/                     # PDF policy documents
├── rag_storage/                  # Vector database storage
├── .env                      # Environment variables
├── .env.example             # Environment template
├── requirements.txt         # Python dependencies
└── README.md               # This file
```

## 🛠️ Configuration

### Environment Variables

Create a `.env` file with:

```bash
OPENAI_API_KEY=your_openai_api_key_here
```

### Pipeline Configuration

Key parameters in `AdvancedRAGPipeline`:

```python
rag = AdvancedRAGPipeline(
    openai_api_key=api_key,
    persist_dir="./storage",           # Vector storage location
    collection_name="rag_collection",  # ChromaDB collection name
    use_reranker=False,                # Enable reranking (requires additional setup)
    llm_model="gpt-4o",               # OpenAI model to use
    embedding_model="text-embedding-3-small",  # Embedding model
    chunk_size=500,                    # Text chunk size
    chunk_overlap=50,                  # Overlap between chunks
    reranker_top_n=5                  # Top results after reranking
)
```

### Chat Commands

- `reset` - Clear conversation history and start fresh
- `config` - Display current pipeline configuration
- `exit` - Quit the application

## 🔧 Advanced Features

### Reranking (Optional)

1. **Enable in configuration:**
   ```python
   rag = AdvancedRAGPipeline(
       use_reranker=True,
       reranker_model="BAAI/bge-reranker-base",
       reranker_top_n=5
   )
   ```

### Custom System Prompt

The system includes a specialized prompt for UM6P policy assistance:

- Identifies as "UM6P policy assistant"
- Only responds based on provided documents
- Includes proper source citations
- Professional, formal tone
- Responds "Sorry I don't know" when information isn't available

## 📊 System Architecture

```
User Query → Context Condensation → Vector Retrieval → Reranking (optional) → LLM Response → Source Citation
```

### Components

1. **Document Processing**: PDFs are chunked and embedded using OpenAI embeddings
2. **Vector Storage**: ChromaDB for persistent vector storage
3. **Retrieval**: Similarity search
4. **Reranking**: Optional sentence-transformer reranking for better relevance
5. **Chat Engine**: CondensePlusContext for conversation memory
6. **Response Generation**: GPT-4 with custom system prompts


## 📚 Dependencies

- `llama-index` - Core RAG framework
- `llama-index-embeddings-openai` - OpenAI embeddings
- `llama-index-llms-openai` - OpenAI LLM integration
- `llama-index-vector-stores-chroma` - ChromaDB integration
- `chromadb` - Vector database
- `python-dotenv` - Environment variable management
- `pypdf` - PDF processing
