aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.3.2
aiosqlite==0.21.0
annotated-types==0.7.0
anyio==4.9.0
attrs==25.3.0
backoff==2.2.1
banks==2.1.3
bcrypt==4.3.0
beautifulsoup4==4.13.4
build==1.2.2.post1
cachetools==5.5.2
certifi==2025.6.15
charset-normalizer==3.4.2
chromadb==1.0.13
click==8.2.1
colorama==0.4.6
coloredlogs==15.0.1
dataclasses-json==0.6.7
deprecated==1.2.18
dirtyjson==1.0.8
distro==1.9.0
durationpy==0.10
fastapi==0.115.14
filelock==3.18.0
filetype==1.2.0
flatbuffers==25.2.10
frozenlist==1.7.0
fsspec==2025.5.1
google-auth==2.40.3
googleapis-common-protos==1.70.0
greenlet==3.2.3
griffe==1.7.3
grpcio==1.73.1
h11==0.16.0
hf-xet==1.1.5
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.33.1
humanfriendly==10.0
idna==3.10
importlib-metadata==8.7.0
importlib-resources==6.5.2
jinja2==3.1.6
jiter==0.10.0
joblib==1.5.1
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
kubernetes==33.1.0
llama-cloud==0.1.26
llama-cloud-services==0.6.34
llama-index==0.12.44
llama-index-agent-openai==0.4.12
llama-index-cli==0.4.3
llama-index-core==0.12.44
llama-index-embeddings-openai==0.3.1
llama-index-indices-managed-llama-cloud==0.7.7
llama-index-instrumentation==0.2.0
llama-index-llms-openai==0.4.7
llama-index-multi-modal-llms-openai==0.5.1
llama-index-program-openai==0.3.2
llama-index-question-gen-openai==0.3.1
llama-index-readers-file==0.4.9
llama-index-readers-llama-parse==0.4.0
llama-index-vector-stores-chroma==0.4.2
llama-index-workflows==1.0.1
llama-parse==0.6.34
markdown-it-py==3.0.0
markupsafe==3.0.2
marshmallow==3.26.1
mdurl==0.1.2
mmh3==5.1.0
mpmath==1.3.0
multidict==6.6.3
mypy-extensions==1.1.0
nest-asyncio==1.6.0
networkx==3.5
nltk==3.9.1
numpy==2.3.1
nvidia-cublas-cu12==12.6.4.1
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==9.5.1.17
nvidia-cufft-cu12==11.3.0.4
nvidia-cufile-cu12==1.11.1.6
nvidia-curand-cu12==10.3.7.77
nvidia-cusolver-cu12==11.7.1.2
nvidia-cusparse-cu12==12.5.4.2
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
oauthlib==3.3.1
onnxruntime==1.22.0
openai==1.93.0
opentelemetry-api==1.34.1
opentelemetry-exporter-otlp-proto-common==1.34.1
opentelemetry-exporter-otlp-proto-grpc==1.34.1
opentelemetry-proto==1.34.1
opentelemetry-sdk==1.34.1
opentelemetry-semantic-conventions==0.55b1
orjson==3.10.18
overrides==7.7.0
packaging==25.0
pandas==2.2.3
pillow==11.2.1
platformdirs==4.3.8
posthog==6.0.0
propcache==0.3.2
protobuf==5.29.5
pyasn1==0.6.1
pyasn1-modules==0.4.2
pybase64==1.4.1
pydantic==2.11.7
pydantic-core==2.33.2
pygments==2.19.2
pypdf==5.7.0
pypika==0.48.9
pyproject-hooks==1.2.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
pytz==2025.2
pyyaml==6.0.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
requests-oauthlib==2.0.0
rich==14.0.0
rpds-py==0.25.1
rsa==4.9.1
safetensors==0.5.3
scikit-learn==1.7.0
scipy==1.16.0
sentence-transformers==4.1.0
setuptools==80.9.0
shellingham==1.5.4
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
sqlalchemy==2.0.41
starlette==0.46.2
striprtf==0.0.26
sympy==1.14.0
tenacity==9.1.2
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.2
torch==2.7.1
tqdm==4.67.1
transformers==4.53.0
triton==3.3.1
typer==0.16.0
typing-extensions==4.14.0
typing-inspect==0.9.0
typing-inspection==0.4.1
tzdata==2025.2
urllib3==2.5.0
uvicorn==0.35.0
uvloop==0.21.0
watchfiles==1.1.0
websocket-client==1.8.0
websockets==15.0.1
wrapt==1.17.2
yarl==1.20.1
zipp==3.23.0
llama-index-eval-base
